---
description: 
globs: 
alwaysApply: false
---
# 極簡計算機 iOS App - UI 設計指南

## 設計原則

1. **極簡主義**
   * 保持 UI 簡潔、乾淨
   * 只顯示必要的元素
   * 避免複雜的裝飾和過度的動畫

2. **一致性**
   * 所有元素應保持一致的視覺風格
   * 使用統一的顏色方案和字體
   * 保持間距和對齊的一致性

3. **可用性**
   * 按鈕尺寸適合手指點擊 (最小 44pt x 44pt)
   * 提供清晰的視覺反饋
   * 確保文本可讀性 (最小字體大小 16pt)

4. **適配性**
   * 使用 AutoInch 框架確保各種 iPhone 尺寸的精確等比例適配
   * 採用相對尺寸設計，而非固定尺寸
   * 支持不同螢幕比例的設備

## 尺寸適配工具

專案使用 [AutoInch](mdc:https:/github.com/lixiang1994/AutoInch) 框架進行尺寸適配：

### 基本用法

* **數值轉換**: 使用 `.auto()` 方法轉換數值
  ```swift
  // 固定值 20 轉換為適應當前設備尺寸的值
  titleLabel.font = .systemFont(ofSize: 20.auto(), weight: .medium)
  
  // 設置適合當前設備的邊距
  view.snp.makeConstraints { make in
      make.left.right.equalToSuperview().inset(15.auto())
  }
  ```

### 適配策略

* **按鈕尺寸**: 使用 `.auto()` 計算適合的按鈕尺寸
  ```swift
  // 按鈕尺寸
  button.frame.size = CGSize(width: 60.auto(), height: 60.auto())
  
  // 按鈕圓角
  button.layer.cornerRadius = 8.auto()
  ```

* **文字大小**: 根據螢幕尺寸調整文字大小
  ```swift
  // 標題文字
  titleLabel.font = .systemFont(ofSize: 24.auto(), weight: .bold)
  
  // 內容文字
  contentLabel.font = .systemFont(ofSize: 16.auto(), weight: .regular)
  ```

* **間距和邊距**: 保持各種尺寸上的視覺平衡
  ```swift
  // 容器邊距
  containerView.snp.makeConstraints { make in
      make.top.equalTo(20.auto())
      make.left.right.equalToSuperview().inset(15.auto())
  }
  ```

## 顏色方案

### 淺色主題（預設）

* **背景色**: `#F9F9F9` (HexColor.color("F9F9F9"))
* **主要文字**: `#000000` (HexColor.color("000000"))
* **次要文字**: `#888888` (HexColor.color("888888"))
* **按鈕背景**: `#FFFFFF` (HexColor.color("FFFFFF"))
* **按鈕邊框**: `#EEEEEE` (HexColor.color("EEEEEE"))
* **強調色**: `#007AFF` (HexColor.color("007AFF"))
* **錯誤色**: `#FF3B30` (HexColor.color("FF3B30"))

### 深色主題（付費功能）

* **背景色**: `#000000` (HexColor.color("000000"))
* **主要文字**: `#FFFFFF` (HexColor.color("FFFFFF"))
* **次要文字**: `#AAAAAA` (HexColor.color("AAAAAA"))
* **按鈕背景**: `#1C1C1E` (HexColor.color("1C1C1E"))
* **按鈕邊框**: `#333333` (HexColor.color("333333"))
* **強調色**: `#0A84FF` (HexColor.color("0A84FF"))
* **錯誤色**: `#FF453A` (HexColor.color("FF453A"))

## 字體與圖標

* **使用自定義 TTF 字體文件**
  * 所有數字和符號使用自定義字體渲染
  * 使用 [FontLoader.swift](mdc:MinimalistCalculator/Utilities/FontLoader.swift) 加載字體
  * 標準數字字體大小: 24pt (小螢幕) 到 32pt (大螢幕)
  * 字體大小使用 `.auto()` 方法確保適配各種設備

* **圖標系統**
  * 使用 [AppIcons.swift](mdc:MinimalistCalculator/Utilities/AppIcons.swift) 定義圖標
  * 使用 [AppIconsSymbol.swift](mdc:MinimalistCalculator/Utilities/AppIconsSymbol.swift) 渲染圖標
  * 圖標預設大小: 24pt x 24pt
  * 圖標尺寸使用 `.auto()` 方法適配

## 佈局規範

### 主分頁視圖

* **垂直 TabView 分頁**
  * 使用 [MainPagingView.swift](mdc:MinimalistCalculator/View/MainPagingView.swift) 實現
  * 使用 SwiftUI TabView 代替早期的 ScrollView 實現
  * 配置方式：
    ```swift
    TabView(selection: $viewModel.currentPage) {
        CalculatorView(viewModel: viewModel)
            .tag(0)
        HistoryView(viewModel: viewModel)
            .tag(1)
        SettingsPageView(viewModel: viewModel)
            .tag(2)
    }
    .tabViewStyle(.page(indexDisplayMode: .never))
    .pageOrientation(.vertical)
    ```
  * 頁面切換使用 SwiftUI 內置的分頁效果
  * 分頁指示器放置於右側邊緣
  * 頁面邊緣顯示部分下一頁內容作為提示

### 計算器視圖

* **顯示區域**
  * 頂部顯示區域佔螢幕高度約 30%
  * 使用 [DisplayAreaView.swift](mdc:MinimalistCalculator/View/DisplayAreaView.swift) 實現
  * 包含輸入過程和結果顯示
  * 文本自動縮小以適應長表達式

* **按鈕網格**
  * 使用 4x5 網格佈局
  * 按鈕間距一致: 8.auto() pt
  * 按鈕圓角: 12.auto() pt
  * 操作符按鈕強調顯示
  * 使用 [CalculatorButtonView.swift](mdc:MinimalistCalculator/View/CalculatorButtonView.swift) 實現按鈕

### 設定頁面

* **列表項目**
  * 統一的行高: 60.auto() pt
  * 左側圖標和右側箭頭
  * 分隔線使用淺灰色 (`#EEEEEE`)
  * 使用圓角容器 (16.auto() pt) 包裹列表項目

* **分組與標題**
  * 使用分組列表樣式
  * 分組標題使用小型大寫字母
  * 標題顏色使用次要文字顏色

### 歷史記錄視圖

* **列表項目**
  * 左側顯示計算表達式
  * 右側顯示結果
  * 使用 [HistoryEntryRowView.swift](mdc:MinimalistCalculator/View/HistoryEntryRowView.swift) 實現
  * 行高: 54.auto() pt
  * 滑動操作: 向左滑動顯示刪除

* **空狀態**
  * 當無歷史記錄時顯示提示文本
  * 居中顯示圖標和文字
  * 使用次要文字顏色

## 交互設計

* **按鈕反饋**
  * 點擊時提供輕微的視覺反饋
  * 使用 `.opacity(0.7)` 或 `.scaleEffect(0.95)` 實現
  * 反饋動畫時長: 0.1秒

* **分頁滾動行為**
  * 使用 TabView 的內置分頁功能
  * 垂直方向滑動切換頁面
  * 自動對齊到整頁
  * 支持快速滑動跳過中間頁面
  * 分頁指示器顯示當前頁面位置

* **轉場動畫**
  * 使用 `withAnimation(.easeInOut(duration: 0.3))` 包裝視圖轉換
  * 頁面切換使用漸變效果
  * 模態視圖使用上滑效果

* **手勢識別**
  * 雙指捏合重置計算器
  * 長按數字可複製到剪貼板
  * 向下滑動隱藏鍵盤 (如適用)

## 響應式設計

* **支持所有 iPhone 尺寸**
  * 使用 AutoInch 框架進行精確適配
  * 使用相對尺寸 (例如使用螢幕寬度的百分比)
  * 在更小的螢幕上適當縮小元素
  * 支持橫屏和縱屏模式

* **適配 iPad**
  * 在 iPad 上使用更大的按鈕和字體
  * 利用額外的屏幕空間顯示更多功能

* **安全區域適配**
  * 尊重系統安全區域
  * 使用 `.safeAreaInset()` 處理底部 home 指示器

## 無障礙設計

* **足夠的對比度**
  * 確保文本與背景有足夠的對比度 (至少 4.5:1)
  * 遵循 WCAG 2.1 AA 級標準

* **支持 VoiceOver**
  * 為所有交互元素添加適當的標籤
  * 使用 `.accessibilityLabel()` 和 `.accessibilityHint()`
  * 為計算結果提供有意義的 VoiceOver 讀取

* **動態字型**
  * 支持系統動態字型大小
  * 測試不同字型大小設置下的 UI 呈現

## 實現方式

* 所有 UI 元素應使用 SwiftUI 原生組件或自定義視圖實現
* 避免使用 UIKit 組件，除非絕對必要
* 顏色定義應使用 [HexColor.swift](mdc:MinimalistCalculator/Utilities/HexColor.swift) 工具類
* 元件應遵循可重用原則，使用參數化設計
* 尺寸值使用 AutoInch 框架的 `.auto()` 方法進行適配

## 垂直分頁的遷移說明

專案早期使用自定義的 ScrollView 實現垂直分頁滾動，現已遷移到 TabView 實現：

### 優勢比較

| 功能 | ScrollView 實現 | TabView 實現 |
|------|---------------|-------------|
| 分頁滾動 | 需自定義實現 | 內置支持 |
| 分頁指示器 | 需自定義實現 | 內置提供 |
| 內存管理 | 需自行優化 | 系統優化 |
| 性能表現 | 較低 | 較高 |
| 實現複雜度 | 高 | 低 |
| 手勢處理 | 需自定義 | 內置支持 |

### 遷移注意事項

* 所有原先依賴於 ScrollView 的自定義行為需重新評估
* 新功能開發應直接使用 TabView 實現
* 不要嘗試混合使用 ScrollView 和 TabView 進行分頁

## 性能優化

* **避免在滾動時進行複雜計算**
  * 在頁面切換前或切換後執行，避免影響滾動流暢度
  
* **使用 `@State` 而非 `@ObservedObject` 管理本地視圖狀態**
  * 減少不必要的視圖刷新
  
* **使用 LazyVStack 和 LazyHStack 處理長列表**
  * 歷史記錄列表應使用 LazyVStack
  
* **自動縮放的優化**
  * 配合 AutoInch 使用動態文本大小時注意效能
  * 避免頻繁調整文本大小
  
* **確保動畫在 60fps 運行流暢**
  * 複雜視圖可考慮使用 `drawingGroup()` 提升性能
