---
description: 
globs: 
alwaysApply: true
---
# PRD: 極簡計算機 iOS App (iPhone) - SwiftUI MVP 版本

**文件版本:** 1.6
**日期:** 2023-10-27
**作者:** (您的姓名/團隊名稱)
**開發約束:** **嚴格遵循本 PRD 定義的功能範圍，不進行任何功能性增刪。開發過程中需在程式碼中添加清晰註解。必須使用用戶提供的自定義 TTF 字體文件以及 PRD 中明確指定的 `AppIcons` 結構體進行圖標和數字映射。開發起點為 SwiftUI "Hello World" 專案。優先考慮使用簡單直接的開發方式，目標是快速交付 MVP。使用 **RevenueCat** 處理 IAP。計算引擎優先使用 **簡單可靠的第三方庫** (通過 CocoaPods 引入)，若無合適則實現基礎功能。**

## 1. 簡介 (Introduction)

本文件定義一款專為 iPhone 設計的極簡主義 iOS 計算機應用程式，基於 SwiftUI 框架開發，以快速實現 MVP 為首要目標。核心是提供純粹的計算體驗，採用完全自定義 UI（預設白底黑字），不使用標準 SwiftUI 控件預設樣式。所有可視元素通過**用戶提供的自定義 TTF 圖標字體**（並**嚴格遵循 PRD Section 8.2.1 指定的 `AppIcons` 結構體映射**）渲染。提供基本計算（含括號、退格）、歷史記錄、輸入過程顯示。支持多語言，通過 **RevenueCat** 實現的付費訂閱（月/年）解鎖深色主題。

## 2. 目標 (Goals)

*   快速交付 MVP。
*   提供無干擾、高效的計算工具。
*   建立獨特、一致的視覺標識（**基於提供的 TTF 和 AppIcons**）。
*   通過訂閱模式驗證商業模式。
*   服務全球 iPhone 用戶。
*   利用 SwiftUI 和成熟第三方庫 (RevenueCat, Math Parser) 簡化開發。

## 3. 目標受眾 (Target Audience)

*   追求極簡設計的 iPhone 用戶。
*   需要基礎計算、括號、退格的用戶。
*   尋找無廣告替代品的用戶。
*   早期採用者，願意為設計/體驗付費訂閱的用戶。

## 4. 功能需求 (Functional Requirements) - **[不可更改範圍]**

### 4.1 核心計算機功能 (Core Calculator)

*   **FR1.1 輸入按鈕:** `0`-`9`, `+`, `-`, `*`, `/`, `=`, `C`, `(`, `)`, `<binary data, 1 bytes><binary data, 1 bytes><binary data, 1 bytes>`, `.` (**必須**使用提供的 TTF 及 `AppIcons` 映射渲染)。
*   **FR1.2 運算邏輯:** 支持帶優先級的四則運算和括號 (優先使用第三方庫)。
*   **FR1.3 顯示:** 主顯示區，輸入過程區 (**必須**使用提供的 TTF 及 `AppIcons` 映射渲染數字和符號)。處理錯誤和長內容。

### 4.2 計算歷史紀錄 (Calculation History)

*   **FR2.1 儲存:** 自動儲存最近 100 條。
*   **FR2.2 顯示:** 入口按鈕 (**使用 AppIcons**)，列表展示 (**使用 AppIcons** 渲染內容)。
*   **FR2.3 互動:** 點擊填入，清除歷史（清除按鈕**使用 AppIcons**，需在 `AppIcons` 中定義對應圖標，如 `static let clearHistory: String = "\u{E9...}"`)。 *(待確認是否有此圖標)*

### 4.3 付費訂閱功能 (Paid Subscription - Dark Theme)

*   **FR3.1 內容:** 解鎖「深色主題」。
*   **FR3.2 訂閱選項:** 月度/年度。
*   **FR3.3 購買流程:** **RevenueCat**。
*   **FR3.4 功能解鎖:** 立即啟用。
*   **FR3.5 恢復購買:** **RevenueCat**。
*   **FR3.6 狀態顯示:** 清晰展示。
*   **FR3.7 訂閱管理:** 跳轉入口。

### 4.4 多語言支援 (Multi-language Support)

*   **FR4.1 語言:** en, zh-Hant。
*   **FR4.2 本地化:** UI 文本, App Store 元數據。
*   **FR4.3 機制:** SwiftUI 本地化。

## 5. 非功能性需求 (Non-Functional Requirements)

*   **NFR1 UI/UX 風格:** 極簡。白/黑主題。無特效。佈局整潔。互動反饋極簡。
*   **NFR2 自定義 UI 與字體 (SwiftUI):**
    *   **強制要求:** 核心界面使用自定義外觀。**必須使用用戶提供的 TTF 字體文件**。**必須使用 PRD Section 8.2.1 中定義的 `AppIcons` 結構體** 作為訪問 TTF 字形的唯一來源。
*   **NFR3 性能:** 快速響應，流暢 (MVP階段可接受優化空間)。
*   **NFR4 平台:** **僅限 iPhone**, iOS 15.0+。
*   **NFR5 開發規範:** Swift, SwiftUI；**MVVM**；聲明式 UI；**強制註解**；**嚴格遵循 PRD**；**追求簡潔快速實現**。
*   **NFR6 可訪問性:** 提供基礎 VoiceOver 支持。
*   **NFR7 依賴管理 (CocoaPods):** `RevenueCat/purchases-ios` (必需)；數學解析庫 (優先)；`CocoaLumberjack` (可選)。

## 6. 貨幣化 (Monetization)

*   免費下載 + **RevenueCat** 驅動的 IAP 訂閱。
*   內容：深色主題。
*   定價：$9.99 USD/月，$107.89 USD/年。

## 7. UI/UX 設計細節 (UI/UX Design - High Level)

*   佈局：`Grid`, `VStack`/`HStack`；上下顯示區；極簡入口。
*   一致性：**全局使用提供的 TTF 及確認的 `AppIcons` 映射**。
*   主題切換：`@EnvironmentObject` 或類似機制。

## 8. 技術架構 (Technical Architecture - SwiftUI MVP) - **[詳細規劃]**

### 8.1 整體架構模式

*   **模式:** **MVVM with SwiftUI**。

### 8.2 UI 實現細節 (SwiftUI)

*   **佈局:** `Grid`, `VStack`, `HStack`, `Spacer`, `GeometryReader`。
*   **自定義按鈕 (`CalculatorButtonView`):** 使用 `ZStack`, `Text` (with **`AppIcons`**, `.font(.custom(providedFontName, ...))`), `Shape`/`Color`, `.onTapGesture`。
*   **自定義顯示 (`DisplayAreaView`):** 使用 `VStack`, `Text` (with **`AppIcons`**, `.font(.custom(providedFontName, ...))`), `.frame`, `.lineLimit/.minimumScaleFactor` 或 `ScrollView`。
*   **8.2.1 自定義字體圖標映射 (`AppIcons`) - [強制使用此定義]**
    *   **來源:** 用戶已確認提供此 Swift 片段和對應 TTF 文件。
    *   **實現:** 直接將用戶提供的 `AppIcons` 結構體代碼放入項目中 (e.g., `AppIcons.swift`)。
      ```swift
      // File: AppIcons.swift
      // Source: Provided by user, aligns with the custom TTF file.
      // DO NOT MODIFY unless TTF file changes and user provides updated mapping.
      struct AppIcons {
            // MARK: - Digits & Decimal
            static let decimal: String          = "\u{E900}" // .
            static let zero: String             = "\u{E901}" // 0
            static let one: String              = "\u{E902}" // 1
            static let two: String              = "\u{E903}" // 2
            static let three: String            = "\u{E904}" // 3
            static let four: String             = "\u{E905}" // 4
            static let five: String             = "\u{E906}" // 5
            static let six: String              = "\u{E907}" // 6
            static let seven: String            = "\u{E908}" // 7
            static let eight: String            = "\u{E909}" // 8
            static let nine: String             = "\u{E90A}" // 9

            // MARK: - Control & Operators
            static let backspace: String        = "\u{E90B}" // <binary data, 1 bytes><binary data, 1 bytes><binary data, 1 bytes> (Backspace Icon)
            // static let unused_E90C: String   = "\u{E90C}" // Reserved or unused
            static let parenthesisLeft: String  = "\u{E90D}" // ( (Left Parenthesis Icon)
            static let parenthesisRight: String = "\u{E90E}" // ) (Right Parenthesis Icon)
            static let clear: String            = "\u{E90F}" // C (Clear Icon, likely AC)
            static let division: String         = "\u{E910}" // / (Division Icon)
            static let multiply: String         = "\u{E911}" // * (Multiplication Icon, e.g., ×)
            static let minus: String            = "\u{E912}" // - (Minus Icon)
            static let plus: String             = "\u{E913}" // + (Plus Icon)
            static let equal: String            = "\u{E914}" // = (Equal Icon)

            // MARK: - Other UI Icons (Placeholders - CONFIRM with TTF & User if needed)
            // These need actual codepoints from the provided TTF file if used.
            // static let history: String       = "\u{E915}" // Example: History icon codepoint?
            // static let settings: String      = "\u{E916}" // Example: Settings icon codepoint?
            // static let checkmark: String     = "\u{E917}" // Example: Checkmark for theme selection?
            // static let lock: String          = "\u{E918}" // Example: Lock for dark theme?
            // static let clearHistory: String  = "\u{E919}" // Example: Clear history icon?
      }
      ```
*   **字體加載:** 將**用戶提供的 TTF 文件**添加到項目和 `Info.plist`。在 SwiftUI 中使用 `.font(.custom("YourProvidedFontName-Regular", size: fontSize))`，確保字體名稱 ("YourProvidedFontName-Regular") 與 TTF 文件內部名稱匹配。
*   **主題切換:** (同 V1.4)。

### 8.3 核心邏輯實現

*   **計算引擎 (`CalculatorEngine`):** (同 V1.5) 優先集成第三方庫，備選自建基礎版。
*   **歷史記錄 (`HistoryRepository`):** (同 V1.5) MVP 使用 `UserDefaults`。

### 8.4 訂閱管理 (`IAPHelper` using RevenueCat)

*   **框架:** **RevenueCat/purchases-ios (必需)**。
*   **實現:** (同 V1.5) 初始化，獲取 Offerings，購買，恢復，監聽 CustomerInfo Stream，檢查 Entitlement 激活狀態。

### 8.5 本地化

*   (同 V1.4) SwiftUI `Text("key")` + `.strings` 文件。

### 8.6 依賴管理 (CocoaPods)

*   **`Podfile` 內容:** (同 V1.5，包含 RevenueCat，以及可能選擇的數學解析庫)。

## 9. App Store 上架資訊 (App Store Submission Information)

*(同 v1.3/v1.4，確保信息準確)*

## 10. 未來考量 (Deferred/Out of Scope for MVP)

*   計算引擎優化, 複雜動畫/交互, iPad 支持, 科學計算, Widgets, Watch App, 更多主題/自定義, 測試覆蓋率提升。

## 11. 文件版本歷史 (Document Version History)

*   ... (v1.0 - v1.4) ...
*   **V1.5 (2023-10-27):** 明確 MVP 優先級，確認使用 RevenueCat，計算引擎優先使用庫。
*   **V1.6 (2023-10-27):** **正式確認使用用戶提供的 TTF 文件和 `AppIcons` 結構體**。在 PRD 各環節強調此約束。添加提示，需確認 TTF 中是否包含 History/Settings 等其他 UI 圖標及其碼點。
