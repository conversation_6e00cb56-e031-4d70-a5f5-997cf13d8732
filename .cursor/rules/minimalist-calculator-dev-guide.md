# 極簡計算機 iOS 應用開發規格書

## 1. 總體架構

### 1.1 概述
極簡計算機是一款基於 SwiftUI 開發的 iOS 應用，主要特點是極簡的 UI 設計和流暢的用戶體驗。應用採用了垂直分頁滾動設計，包含計算器頁面和設置頁面。本專案主要基於：

- **開發框架**：SwiftUI
- **Target iOS 版本**：iOS 15.0+
- **設計模式**：MVVM (Model-View-ViewModel)
- **特色**：自定義字體圖標、垂直分頁滾動、自定義 UI 組件

### 1.2 文件結構
```
MinimalistCalculator/
├── App/
│   └── MinimalistCalculatorApp.swift          # 應用入口
├── Model/
│   ├── CalculationHistory.swift               # 歷史記錄模型
│   └── ButtonType.swift                       # 按鈕類型枚舉
├── ViewModel/
│   └── CalculatorViewModel.swift              # 計算器視圖模型
├── View/
│   ├── MainPagingView.swift                   # 主分頁視圖
│   ├── Page1ContentView.swift                 # 計算器頁面
│   ├── SettingsPageView.swift                 # 設置頁面
│   ├── CalculatorView.swift                   # 計算器視圖
│   ├── DisplayAreaView.swift                  # 顯示區域
│   ├── CalculatorButtonView.swift             # 計算器按鈕
│   ├── HistoryView.swift                      # 歷史記錄視圖
│   └── HistoryEntryRowView.swift              # 歷史條目行
├── Services/
│   ├── CalculatorEngine.swift                 # 計算引擎
│   └── HistoryRepository.swift                # 歷史記錄存儲
├── Utilities/
│   ├── AppIcons.swift                         # 自定義圖標映射
│   └── AppIconsSymbol.swift                   # 圖標顯示幫助類
└── Resources/
    └── Fonts/
        └── CustomIcons.ttf                    # 自定義圖標字體
```

## 2. 核心組件詳解

### 2.1 自定義圖標系統

#### 2.1.1 `AppIcons.swift`
自定義圖標字符映射結構體，將 Unicode 碼點映射到計算機圖標：

```swift
struct AppIcons {
    // MARK: - Digits & Decimal
    static let decimal: String          = "\u{E900}" // .
    static let zero: String             = "\u{E901}" // 0
    static let one: String              = "\u{E902}" // 1
    // ... 更多數字和符號
    
    // MARK: - Control & Operators
    static let backspace: String        = "\u{E90B}" // ⌫
    static let parenthesisLeft: String  = "\u{E90C}" // (
    // ... 更多控制和運算符
    
    // MARK: - Other UI Icons
    static let clearHistory: String     = "\u{E915}" // 清除歷史
    static let close: String            = "\u{E916}" // 關閉
    // ... 更多 UI 圖標
}
```

#### 2.1.2 `AppIconsSymbol.swift`
自定義圖標顯示幫助類，用於創建使用自定義字體的圖標視圖：

```swift
struct AppIconsSymbol {
    static let fontName = "CustomIcons"
    
    static func createView(for iconString: String, fontSize: CGFloat = 24, color: Color = .black) -> some View {
        Text(iconString)
            .font(.custom(fontName, size: fontSize))
            .foregroundColor(color)
    }
}
```

#### 2.1.3 字體集成步驟
1. 將自定義 TTF 文件添加到專案中
2. 更新 Info.plist 添加字體聲明：
   ```xml
   <key>UIAppFonts</key>
   <array>
       <string>CustomIcons.ttf</string>
   </array>
   ```
3. 確保 `AppIconsSymbol.fontName` 匹配 TTF 文件的實際字體名稱

### 2.2 垂直分頁滾動實現

#### 2.2.1 `MainPagingView.swift`
主視圖容器，實現了垂直分頁滾動效果：

```swift
struct MainPagingView: View {
    @StateObject private var viewModel = CalculatorViewModel()
    
    var body: some View {
        GeometryReader { geometry in
            ScrollViewReader { proxy in
                ScrollView(.vertical, showsIndicators: false) {
                    VStack(spacing: 0) {
                        // 第一頁：計算器
                        Page1ContentView(viewModel: viewModel)
                            .frame(height: geometry.size.height)
                            .id("page1")
                            .overlay(alignment: .bottom) {
                                // 向下滾動按鈕
                                HStack {
                                    Button {
                                        withAnimation {
                                            proxy.scrollTo("page2", anchor: .top)
                                        }
                                    } label: {
                                        AppIconsSymbol.createView(for: AppIcons.scrollDown, fontSize: 44, color: Color(red: 136/255, green: 136/255, blue: 136/255))
                                    }
                                    Spacer()
                                }
                                .padding(.bottom, 30)
                                .padding(.horizontal, 60)
                            }
                        
                        // 第二頁：設置
                        SettingsPageView(viewModel: viewModel, scrollToTopAction: {
                            withAnimation {
                                proxy.scrollTo("page1", anchor: .top)
                            }
                        })
                        .frame(height: geometry.size.height)
                        .id("page2")
                    }
                }
                .scrollTargetBehavior(.paging) // 關鍵：實現分頁滾動行為
                .ignoresSafeArea(edges: .vertical)
            }
        }
        .background(Color(.systemBackground))
    }
}
```

關鍵實現要點：
- 使用 `GeometryReader` 獲取螢幕高度
- 使用 `ScrollViewReader` 實現程式控制滾動
- 使用 `.scrollTargetBehavior(.paging)` 實現分頁效果
- 頁面間導航通過 `proxy.scrollTo()` 實現
- 每個頁面使用固定高度 `geometry.size.height`

### 2.3 計算器核心

#### 2.3.1 `ButtonType.swift`
按鈕類型定義，與圖標字體集成：

```swift
enum ButtonType {
    case digit(Int)
    case operation(Operation)
    case equal
    case clear
    case decimal
    case backspace
    case parenthesis(ParenthesisType)
    
    enum Operation {
        case add, subtract, multiply, divide
    }
    
    enum ParenthesisType {
        case left, right
    }
    
    // 重要：將按鈕類型映射到圖標字符
    var iconString: String {
        switch self {
        case .digit(let number):
            switch number {
            case 0: return AppIcons.zero
            case 1: return AppIcons.one
            // ... 更多數字
            default: return ""
            }
        case .operation(let operation):
            switch operation {
            case .add: return AppIcons.plus
            // ... 更多運算符
            }
        // ... 更多按鈕類型
        }
    }
}
```

#### 2.3.2 `CalculatorViewModel.swift`
計算器邏輯和狀態管理：

```swift
class CalculatorViewModel: ObservableObject {
    @Published var inputExpression: String = ""
    @Published var displayValue: String = AppIcons.zero
    @Published var historyEntries: [HistoryEntry] = []
    
    private let calculatorEngine = CalculatorEngine()
    private let historyRepository = HistoryRepository()
    
    init() {
        // 從存儲加載歷史記錄
        loadHistory()
    }
    
    func buttonTapped(buttonType: ButtonType) {
        // 處理按鈕輸入邏輯
        switch buttonType {
        case .digit(let number):
            appendInput(String(number))
        case .operation(let operation):
            // 處理運算符輸入
            // ...
        // ... 處理其他按鈕類型
        }
    }
    
    // 歷史記錄方法
    func loadHistory() {
        historyEntries = historyRepository.loadHistory()
    }
    
    func addToHistory(_ entry: HistoryEntry) {
        historyEntries.insert(entry, at: 0)
        if historyEntries.count > 100 {
            historyEntries.removeLast()
        }
        historyRepository.saveHistory(historyEntries)
    }
    
    // ... 更多計算邏輯和狀態管理方法
}
```

### 2.4 自定義 UI 組件

#### 2.4.1 `CalculatorButtonView.swift`
自定義計算器按鈕視圖：

```swift
struct CalculatorButtonView: View {
    let iconString: String
    let buttonColor: Color
    let textColor: Color
    let fontSize: CGFloat
    let action: () -> Void
    
    init(
        iconString: String,
        buttonColor: Color = Color(.systemGray5),
        textColor: Color = .black,
        fontSize: CGFloat = 24,
        action: @escaping () -> Void
    ) {
        self.iconString = iconString
        self.buttonColor = buttonColor
        self.textColor = textColor
        self.fontSize = fontSize
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(buttonColor)
                
                AppIconsSymbol.createView(for: iconString, fontSize: fontSize, color: textColor)
            }
        }
        .aspectRatio(1.0, contentMode: .fit)
    }
}
```

#### 2.4.2 滾動漸變效果實現
在列表視圖（如 `HistoryView`）中實現頂部和底部的漸變淡出效果：

```swift
ZStack(alignment: .center) {
    // 滾動視圖
    ScrollView {
        // 添加頂部間距
        Color.clear.frame(height: 20)
        
        // 內容
        LazyVStack(spacing: 12) {
            // 列表項目
        }
        .padding(.horizontal)
        
        // 添加底部間距
        Color.clear.frame(height: 20)
    }
    
    // 頂部漸變淡出效果
    VStack {
        LinearGradient(
            gradient: Gradient(colors: [Color(.systemBackground), Color(.systemBackground).opacity(0)]),
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(height: 30)
        Spacer()
    }
    
    // 底部漸變淡出效果
    VStack {
        Spacer()
        LinearGradient(
            gradient: Gradient(colors: [Color(.systemBackground).opacity(0), Color(.systemBackground)]),
            startPoint: .top,
            endPoint: .bottom
        )
        .frame(height: 30)
    }
}
```

## 3. 開發步驟指南

### 3.1 項目初始化與基礎設置

1. **創建新的 SwiftUI 項目**
   ```bash
   # 使用 Xcode 創建新項目，選擇 SwiftUI App 模板
   # Target: iOS 15.0+
   # Interface: SwiftUI
   # Lifecycle: SwiftUI App
   ```

2. **建立文件結構**
   ```bash
   # 創建文件夾結構
   mkdir -p App Model ViewModel View Services Utilities Resources/Fonts
   ```

3. **集成自定義字體**
   - 添加 `CustomIcons.ttf` 到 `Resources/Fonts` 目錄
   - 更新 `Info.plist` 添加 `UIAppFonts` 聲明
   - 創建 `AppIcons.swift` 和 `AppIconsSymbol.swift`

### 3.2 實現自定義 UI 組件

1. **創建 `CalculatorButtonView`**
   - 實現自定義按鈕樣式
   - 集成自定義圖標字體

2. **創建 `DisplayAreaView`**
   - 實現計算器顯示區域
   - 支持表達式和結果顯示

3. **實現基礎 `CalculatorView`**
   - 使用自定義按鈕佈局
   - 實現基礎計算器網格

### 3.3 實現計算器邏輯

1. **定義 `ButtonType` 模型**
   - 創建按鈕類型和操作枚舉
   - 將按鈕類型映射到圖標

2. **創建 `CalculatorViewModel`**
   - 實現計算邏輯
   - 管理應用狀態

3. **實現歷史記錄功能**
   - 創建 `HistoryEntry` 模型
   - 實現存儲和加載功能

### 3.4 實現垂直分頁滾動

1. **創建 `MainPagingView`**
   - 實現垂直滾動容器
   - 添加頁面切換功能

2. **提取 `Page1ContentView`**
   - 從 `CalculatorView` 提取核心功能
   - 調整適應分頁佈局

3. **創建 `SettingsPageView`**
   - 實現設置頁面
   - 添加向上滾動功能

### 3.5 實現進階功能

1. **歷史記錄視圖**
   - 創建 `HistoryView` 和 `HistoryEntryRowView`
   - 實現歷史記錄展示和選擇功能

2. **添加 UI 增強效果**
   - 實現滾動漸變效果
   - 優化動畫和交互

## 4. 關鍵技術實現詳解

### 4.1 創建新項目時的完整起始代碼

#### 4.1.1 `MinimalistCalculatorApp.swift`
```swift
import SwiftUI

@main
struct MinimalistCalculatorApp: App {
    var body: some Scene {
        WindowGroup {
            MainPagingView()
        }
    }
}
```

#### 4.1.2 `AppIcons.swift`
```swift
struct AppIcons {
    // MARK: - Digits & Decimal
    static let decimal: String          = "\u{E900}" // .
    static let zero: String             = "\u{E901}" // 0
    static let one: String              = "\u{E902}" // 1
    static let two: String              = "\u{E903}" // 2
    static let three: String            = "\u{E904}" // 3
    static let four: String             = "\u{E905}" // 4
    static let five: String             = "\u{E906}" // 5
    static let six: String              = "\u{E907}" // 6
    static let seven: String            = "\u{E908}" // 7
    static let eight: String            = "\u{E909}" // 8
    static let nine: String             = "\u{E90A}" // 9
    
    // MARK: - Control & Operators
    static let backspace: String        = "\u{E90B}" // ⌫
    static let parenthesisLeft: String  = "\u{E90C}" // (
    static let parenthesisRight: String = "\u{E90D}" // )
    static let clear: String            = "\u{E90E}" // C
    static let division: String         = "\u{E90F}" // /
    static let multiply: String         = "\u{E910}" // *
    static let minus: String            = "\u{E911}" // -
    static let plus: String             = "\u{E912}" // +
    static let equal: String            = "\u{E913}" // =
    
    // MARK: - Other UI Icons
    static let clearHistory: String     = "\u{E915}" // 清除歷史
    static let close: String            = "\u{E916}" // 關閉
    static let facebook: String         = "\u{E917}" // Facebook
    static let instagram: String        = "\u{E918}" // Instagram
    static let scrollDown: String       = "\u{E919}" // 向下滾動
    static let scrollUp: String         = "\u{E920}" // 向上滾動
}
```

#### 4.1.3 `AppIconsSymbol.swift`
```swift
import SwiftUI

struct AppIconsSymbol {
    static let fontName = "CustomIcons"
    
    static func createView(for iconString: String, fontSize: CGFloat = 24, color: Color = .black) -> some View {
        Text(iconString)
            .font(.custom(fontName, size: fontSize))
            .foregroundColor(color)
    }
}
```

#### 4.1.4 `ButtonType.swift`
```swift
enum ButtonType {
    case digit(Int)
    case operation(Operation)
    case equal
    case clear
    case decimal
    case backspace
    case parenthesis(ParenthesisType)
    
    enum Operation {
        case add, subtract, multiply, divide
    }
    
    enum ParenthesisType {
        case left, right
    }
    
    var iconString: String {
        switch self {
        case .digit(let number):
            switch number {
            case 0: return AppIcons.zero
            case 1: return AppIcons.one
            case 2: return AppIcons.two
            case 3: return AppIcons.three
            case 4: return AppIcons.four
            case 5: return AppIcons.five
            case 6: return AppIcons.six
            case 7: return AppIcons.seven
            case 8: return AppIcons.eight
            case 9: return AppIcons.nine
            default: return ""
            }
        case .operation(let operation):
            switch operation {
            case .add: return AppIcons.plus
            case .subtract: return AppIcons.minus
            case .multiply: return AppIcons.multiply
            case .divide: return AppIcons.division
            }
        case .equal: return AppIcons.equal
        case .clear: return AppIcons.clear
        case .decimal: return AppIcons.decimal
        case .backspace: return AppIcons.backspace
        case .parenthesis(let type):
            switch type {
            case .left: return AppIcons.parenthesisLeft
            case .right: return AppIcons.parenthesisRight
            }
        }
    }
}
```

### 4.2 開始開發的流程建議

1. **環境設置與基礎組件**
   - 先實現自定義字體系統和基礎 UI 組件
   - 確保字體正確加載和顯示

2. **由小到大，逐步構建**
   - 先實現單個功能組件
   - 逐步組合成更複雜的視圖

3. **分階段測試**
   - 每實現一個核心功能就進行測試
   - 確保基礎功能穩定後再添加複雜功能

4. **代碼組織與註釋**
   - 遵循 MVVM 模式清晰組織代碼
   - 添加詳細註釋說明實現邏輯

## 5. 最佳實踐與注意事項

### 5.1 自定義字體使用
- 確保字體文件名稱與代碼中的引用匹配
- 測試不同大小屏幕上的字體渲染
- 考慮可訪問性，提供適當的替代文本

### 5.2 垂直分頁滾動
- 使用 `.scrollTargetBehavior(.paging)` 確保流暢的頁面切換
- 考慮使用漸變覆蓋提升視覺體驗
- 注意安全區域處理，特別是在不同 iPhone 模型上

### 5.3 性能考量
- 使用 `LazyVStack`/`LazyHStack` 優化列表性能
- 避免在 ScrollView 中嵌套過多複雜視圖
- 注意狀態管理和更新頻率

### 5.4 未來擴展方向
- 深色模式支持
- 計算引擎優化
- 本地化支持
- iPad 支持和佈局適配

## 6. 結論

本規格書詳細描述了極簡計算機應用的架構設計和實現方法，特別是自定義字體圖標系統和垂直分頁滾動機制的實現。通過遵循這些指南，開發者可以創建具有一致設計語言和流暢用戶體驗的計算器應用。

專案的核心亮點在於：
1. 優雅的 UI 設計和自定義圖標系統
2. 流暢的垂直分頁滾動體驗
3. 清晰的代碼組織和架構
4. 豐富的自定義 UI 組件

遵循本規格書的指導，可以高效開發出功能完整、體驗優良的極簡計算機應用。 