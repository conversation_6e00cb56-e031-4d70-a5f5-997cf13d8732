# 基於極簡計算機架構的新應用開發指南

## 1. 開發概述

本指南基於極簡計算機應用的架構，提供創建新應用的步驟說明。新應用將保留以下核心組件：
- 垂直分頁滾動結構
- 設置頁面 (SettingsPageView)
- 歷史記錄模型 (但內容為空白)
- 自定義圖標字體系統

我們將省略原計算器的計算功能，專注於建立一個可擴展的應用框架。

## 2. 專案初始化與基礎設置

### 2.1 創建新專案

1. **啟動 Xcode 並創建新項目**
   - 選擇 "App" 模板
   - 產品名稱：根據您的新應用命名
   - 介面選擇：SwiftUI
   - 生命週期：SwiftUI App
   - 語言：Swift
   - 目標平台：iOS
   - 最低部署版本：iOS 15.0+

2. **設置專案文件結構**
   ```bash
   # 在專案根目錄下創建以下文件夾
   mkdir -p App Model ViewModel View Services Utilities Resources/Fonts
   ```

3. **遷移基本文件到適當文件夾**
   - 將 ContentView.swift 移動到 View 文件夾並重命名為 MainView.swift
   - 將 YourAppName.swift 移動到 App 文件夾

### 2.2 集成自定義字體系統

1. **添加字體文件**
   - 將 CustomIcons.ttf 文件添加到 Resources/Fonts 目錄
   - 在 Info.plist 中添加字體聲明：
     ```xml
     <key>UIAppFonts</key>
     <array>
         <string>CustomIcons.ttf</string>
     </array>
     ```

2. **創建圖標映射文件**
   - 在 Utilities 文件夾中創建 AppIcons.swift：
     ```swift
     // AppIcons.swift
     // 自定義圖標字符映射

     struct AppIcons {
         // 核心 UI 圖標
         static let close: String       = "\u{E916}" // 關閉
         static let scrollDown: String  = "\u{E919}" // 向下滾動
         static let scrollUp: String    = "\u{E920}" // 向上滾動
         
         // 社交媒體圖標
         static let facebook: String    = "\u{E917}" // Facebook
         static let instagram: String   = "\u{E918}" // Instagram
         
         // 此處可添加新應用需要的其他圖標
     }
     ```

3. **創建圖標顯示幫助類**
   - 在 Utilities 文件夾中創建 AppIconsSymbol.swift：
     ```swift
     // AppIconsSymbol.swift
     // 自定義圖標顯示幫助類

     import SwiftUI

     struct AppIconsSymbol {
         static let fontName = "CustomIcons"
         
         static func createView(for iconString: String, fontSize: CGFloat = 24, color: Color = .black) -> some View {
             Text(iconString)
                 .font(.custom(fontName, size: fontSize))
                 .foregroundColor(color)
         }
     }
     ```

## 3. 實現核心視圖結構

### 3.1 建立主視圖與垂直分頁滾動

1. **創建 MainViewModel**
   - 在 ViewModel 文件夾中創建 MainViewModel.swift：
     ```swift
     // MainViewModel.swift
     // 主視圖模型

     import SwiftUI
     import Combine

     class MainViewModel: ObservableObject {
         // 基本狀態管理
         @Published var showingSettings: Bool = false
         @Published var historyEntries: [HistoryEntry] = []
         
         // 可以添加更多應用特定的狀態和方法
         
         func loadHistory() {
             // 此處僅保留模型，暫不實現實際加載功能
         }
     }
     ```

2. **創建 MainPagingView**
   - 在 View 文件夾中創建 MainPagingView.swift：
     ```swift
     // MainPagingView.swift
     // 垂直分頁滾動視圖

     import SwiftUI

     struct MainPagingView: View {
         @StateObject private var viewModel = MainViewModel()
         var horizontalPadding: CGFloat = 60
         
         var body: some View {
             GeometryReader { geometry in
                 ScrollViewReader { proxy in
                     ScrollView(.vertical, showsIndicators: false) {
                         VStack(spacing: 0) {
                             // 頁面 1: 主內容
                             Page1ContentView(viewModel: viewModel)
                                 .frame(height: geometry.size.height)
                                 .id("page1")
                                 .overlay(alignment: .bottom) {
                                     HStack {
                                         Button {
                                             withAnimation {
                                                 proxy.scrollTo("page2", anchor: .top)
                                             }
                                         } label: {
                                             AppIconsSymbol.createView(
                                                 for: AppIcons.scrollDown, 
                                                 fontSize: 44, 
                                                 color: Color(red: 136/255, green: 136/255, blue: 136/255)
                                             )
                                         }
                                         Spacer()
                                     }
                                     .padding(.bottom, 30)
                                     .padding(.horizontal, horizontalPadding)
                                 }
                             
                             // 頁面 2: 設置頁面
                             SettingsPageView(viewModel: viewModel, scrollToTopAction: {
                                 withAnimation {
                                     proxy.scrollTo("page1", anchor: .top)
                                 }
                             })
                             .frame(height: geometry.size.height)
                             .id("page2")
                         }
                     }
                     .scrollTargetBehavior(.paging)
                     .ignoresSafeArea(edges: .vertical)
                 }
             }
             .background(Color(.systemBackground))
         }
     }

     // 預覽
     #Preview {
         MainPagingView()
     }
     ```

3. **創建 Page1ContentView**
   - 在 View 文件夾中創建 Page1ContentView.swift：
     ```swift
     // Page1ContentView.swift
     // 主頁面內容

     import SwiftUI

     struct Page1ContentView: View {
         @ObservedObject var viewModel: MainViewModel
         
         var body: some View {
             VStack {
                 // 這裡可以自定義您的主視圖內容
                 // 以下只是一個佔位示例
                 
                 Spacer()
                 
                 Text("主視圖內容")
                     .font(.largeTitle)
                     .padding()
                 
                 Text("向下滾動查看設置")
                     .font(.subheadline)
                     .foregroundColor(.secondary)
                 
                 Spacer()
             }
             .padding()
             .background(Color(.systemBackground))
         }
     }

     // 預覽
     #Preview {
         Page1ContentView(viewModel: MainViewModel())
     }
     ```

### 3.2 實現設置頁面

1. **創建 SettingsPageView**
   - 在 View 文件夾中創建 SettingsPageView.swift：
     ```swift
     // SettingsPageView.swift
     // 設置頁面

     import SwiftUI

     struct SettingsPageView: View {
         @ObservedObject var viewModel: MainViewModel
         var scrollToTopAction: () -> Void
         @State private var showingHistory: Bool = false
         
         var body: some View {
             VStack(spacing: 0) {
                 // 返回按鈕
                 HStack {
                     Spacer()
                     Button {
                         scrollToTopAction()
                     } label: {
                         AppIconsSymbol.createView(
                             for: AppIcons.scrollUp, 
                             fontSize: 44, 
                             color: Color(red: 136/255, green: 136/255, blue: 136/255)
                         )
                     }
                 }
                 .padding(.top, 50)
                 .padding(.trailing, 60)
                 
                 // 設置選項列表
                 VStack(alignment: .leading, spacing: 0) {
                     SettingsRow(title: "歷史記錄") {
                         showingHistory = true
                     }
                     Divider().padding(.leading)
                     
                     SettingsRow(title: "關於") {
                         // 關於頁面的操作
                         print("About Tapped")
                     }
                     Divider().padding(.leading)
                     
                     SettingsRow(title: "設置") {
                         // 設置的操作
                         print("Settings Tapped")
                     }
                     Divider().padding(.leading)
                 }
                 .background(Color(UIColor.secondarySystemGroupedBackground))
                 .cornerRadius(10)
                 .padding(.horizontal)
                 .padding(.top)
                 
                 Spacer()
                 
                 // 社群媒體圖示
                 HStack(spacing: 20) {
                     Button {
                         // Facebook 操作
                         print("Facebook")
                     } label: {
                         AppIconsSymbol.createView(
                             for: AppIcons.facebook,
                             fontSize: 30,
                             color: .primary
                         )
                     }
                     
                     Button {
                         // Instagram 操作
                         print("Instagram")
                     } label: {
                         AppIconsSymbol.createView(
                             for: AppIcons.instagram,
                             fontSize: 30,
                             color: .primary
                         )
                     }
                 }
                 .padding(.bottom, 50)
             }
             .frame(maxWidth: .infinity, maxHeight: .infinity)
             .background(Color(UIColor.systemGroupedBackground))
             .sheet(isPresented: $showingHistory) {
                 HistoryView(viewModel: viewModel, onSelectHistory: {
                     showingHistory = false
                     scrollToTopAction()
                 })
             }
         }
     }

     // 設置行視圖
     struct SettingsRow: View {
         let title: String
         let action: () -> Void
         
         var body: some View {
             Button(action: action) {
                 HStack {
                     Text(title)
                         .foregroundColor(.primary)
                     Spacer()
                     Image(systemName: "chevron.right")
                         .foregroundColor(.secondary)
                 }
                 .padding()
                 .contentShape(Rectangle())
             }
             .buttonStyle(.plain)
         }
     }

     // 預覽
     #Preview {
         SettingsPageView(
             viewModel: MainViewModel(),
             scrollToTopAction: {}
         )
     }
     ```

### 3.3 建立歷史記錄組件

1. **創建歷史記錄模型**
   - 在 Model 文件夾中創建 HistoryEntry.swift：
     ```swift
     // HistoryEntry.swift
     // 歷史記錄模型

     import Foundation

     struct HistoryEntry: Identifiable, Codable {
         let id: UUID
         let timestamp: Date
         let title: String
         let subtitle: String
         
         init(id: UUID = UUID(), timestamp: Date = Date(), title: String, subtitle: String) {
             self.id = id
             self.timestamp = timestamp
             self.title = title
             self.subtitle = subtitle
         }
     }
     ```

2. **創建歷史記錄視圖**
   - 在 View 文件夾中創建 HistoryView.swift 和 HistoryEntryRowView.swift：
     ```swift
     // HistoryView.swift
     // 歷史記錄視圖

     import SwiftUI

     struct HistoryView: View {
         @ObservedObject var viewModel: MainViewModel
         @Environment(\.dismiss) private var dismiss
         var onSelectHistory: (() -> Void)?
         
         // 初始化函數
         init(viewModel: MainViewModel, onSelectHistory: (() -> Void)? = nil) {
             self.viewModel = viewModel
             self.onSelectHistory = onSelectHistory
         }
         
         var body: some View {
             ZStack(alignment: .top) {
                 // 主要內容
                 VStack(spacing: 0) {
                     // 自定義頂部工具欄
                     HStack {
                         // 關閉按鈕
                         Button(action: {
                             dismiss()
                         }) {
                             AppIconsSymbol.createView(for: AppIcons.close, fontSize: 44, color: .black)
                         }
                         
                         Spacer()
                     }
                     .padding(.horizontal, 60)
                     .padding(.top, 60)
                     .padding(.bottom, 20)
                     
                     // 內容區域
                     if viewModel.historyEntries.isEmpty {
                         // 沒有歷史記錄
                         Spacer()
                         Text("暫無記錄")
                             .font(.system(size: 20, weight: .regular))
                             .foregroundColor(.primary)
                         Spacer()
                     } else {
                         // 有歷史記錄時才使用 ScrollView
                         ZStack(alignment: .center) {
                             // 歷史記錄 ScrollView
                             ScrollView {
                                 // 添加頂部間距
                                 Color.clear.frame(height: 20)
                                 
                                 // 顯示歷史記錄列表
                                 LazyVStack(spacing: 12) {
                                     ForEach(viewModel.historyEntries) { entry in
                                         HistoryEntryRowView(entry: entry) {
                                             // 點擊歷史記錄項目的操作
                                             if let callback = onSelectHistory {
                                                 callback()
                                             } else {
                                                 dismiss()
                                             }
                                         }
                                     }
                                 }
                                 .padding(.horizontal)
                                 
                                 // 添加底部間距
                                 Color.clear.frame(height: 20)
                             }
                             
                             // 頂部淡出漸層
                             VStack {
                                 LinearGradient(
                                     gradient: Gradient(colors: [Color(.systemBackground), Color(.systemBackground).opacity(0)]),
                                     startPoint: .top,
                                     endPoint: .bottom
                                 )
                                 .frame(height: 30)
                                 Spacer()
                             }
                             
                             // 底部淡出漸層
                             VStack {
                                 Spacer()
                                 LinearGradient(
                                     gradient: Gradient(colors: [Color(.systemBackground).opacity(0), Color(.systemBackground)]),
                                     startPoint: .top,
                                     endPoint: .bottom
                                 )
                                 .frame(height: 30)
                             }
                         }
                     }
                 }
                 .padding(.bottom, 40)
             }
             .edgesIgnoringSafeArea(.all)
             .background(Color(.systemBackground))
         }
     }

     // 預覽
     #Preview {
         HistoryView(viewModel: MainViewModel())
     }
     ```

     ```swift
     // HistoryEntryRowView.swift
     // 歷史記錄行視圖

     import SwiftUI

     struct HistoryEntryRowView: View {
         let entry: HistoryEntry
         let onTap: () -> Void
         
         var body: some View {
             Button(action: onTap) {
                 VStack(alignment: .leading, spacing: 8) {
                     // 標題行
                     Text(entry.title)
                         .font(.headline)
                         .foregroundColor(.primary)
                     
                     // 副標題行
                     Text(entry.subtitle)
                         .font(.subheadline)
                         .foregroundColor(.secondary)
                     
                     // 時間戳
                     Text(formattedDate(entry.timestamp))
                         .font(.caption)
                         .foregroundColor(.secondary)
                 }
                 .frame(maxWidth: .infinity, alignment: .leading)
                 .padding()
                 .background(Color(.secondarySystemBackground))
                 .cornerRadius(10)
             }
             .buttonStyle(.plain)
         }
         
         // 格式化日期
         private func formattedDate(_ date: Date) -> String {
             let formatter = DateFormatter()
             formatter.dateStyle = .medium
             formatter.timeStyle = .short
             return formatter.string(from: date)
         }
     }

     // 預覽
     #Preview {
         HistoryEntryRowView(
             entry: HistoryEntry(
                 title: "範例記錄",
                 subtitle: "這是一條範例記錄的詳細內容"
             ),
             onTap: {}
         )
         .padding()
         .previewLayout(.sizeThatFits)
     }
     ```

### 3.4 更新 App 入口點

1. **修改 App 入口文件**
   - 在 App 文件夾中更新您的 App 入口文件：
     ```swift
     // YourAppNameApp.swift
     // 應用入口

     import SwiftUI

     @main
     struct YourAppNameApp: App {
         var body: some Scene {
             WindowGroup {
                 MainPagingView()
             }
         }
     }
     ```

## 4. 構建與測試

1. **確保自定義字體正確加載**
   - 檢查 Info.plist 中的字體聲明
   - 確認 AppIconsSymbol.fontName 與實際字體名稱匹配

2. **運行應用進行基本測試**
   - 確認垂直分頁滾動正常工作
   - 測試頁面間的導航
   - 驗證設置頁面正確顯示
   - 檢查歷史記錄視圖（即使為空）

3. **調整佈局和樣式**
   - 根據設備尺寸測試佈局
   - 調整間距和元素大小
   - 確保設計一致性

## 5. 擴展與自定義

### 5.1 自定義主視圖內容

- 替換 Page1ContentView 中的佔位內容為您的實際功能
- 添加新的視圖組件和狀態管理

### 5.2 添加更多設置選項

- 在 SettingsPageView 中添加更多設置選項
- 實現設置的狀態管理和持久化

### 5.3 擴展歷史記錄功能

- 實現實際的歷史記錄數據管理
- 添加歷史記錄的增加、刪除和過濾功能

## 6. 最佳實踐和注意事項

1. **自定義字體使用**
   - 確保使用的圖標與字體文件中定義的一致
   - 考慮在不同設備上的字體顯示效果

2. **垂直分頁滾動**
   - 注意安全區域處理
   - 確保滾動流暢且與導航按鈕協調

3. **代碼組織**
   - 遵循 MVVM 模式
   - 保持視圖模型的簡潔和可測試性
   - 使用適當的註釋說明關鍵代碼

4. **性能考量**
   - 使用 LazyVStack/LazyHStack 加載列表
   - 避免過度使用嵌套視圖和複雜計算

## 7. 結論

以上步驟提供了基於極簡計算機架構創建新應用的完整指南。通過保留垂直分頁滾動、設置頁面和歷史記錄模型，同時省略計算功能，您可以快速構建一個具有相同架構的新應用框架。這個框架提供了良好的擴展性，允許您根據需求添加新的功能和視圖。 