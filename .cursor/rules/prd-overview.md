---
description: 
globs: 
alwaysApply: false
---
# 極簡計算機 iOS App - PRD 概述

## 產品定義

極簡計算機是一款專為 iPhone 設計的 iOS 應用程式，基於 SwiftUI 框架開發，以快速實現 MVP 為首要目標。核心是提供純粹的計算體驗，採用完全自定義 UI（預設白底黑字），不使用標準 SwiftUI 控件預設樣式。所有可視元素通過自定義 TTF 圖標字體渲染。

## 核心功能

1. **基本計算功能**
   * 數字按鈕: `0`-`9`
   * 運算符: `+`, `-`, `*`, `/`, `=`
   * 控制按鈕: `C`, `(`, `)`, `<退格>`, `.`
   * 運算邏輯: 支持帶優先級的四則運算和括號
   * 顯示區域: 主顯示區，輸入過程區

2. **計算歷史紀錄**
   * 自動儲存最近 100 條計算記錄
   * 列表展示歷史記錄
   * 點擊填入功能
   * 清除歷史功能

3. **付費訂閱功能**
   * 解鎖「深色主題」
   * 月度/年度訂閱選項
   * 使用 RevenueCat 處理 IAP

4. **多語言支援**
   * 英文 (en)
   * 繁體中文 (zh-Hant)

## UI/UX 設計

1. **極簡主義設計**
   * 純粹的計算體驗
   * 完全自定義 UI
   * 預設白底黑字，付費解鎖深色主題

2. **自定義 UI 與字體**
   * 使用自定義 TTF 字體文件
   * 使用 `AppIcons` 結構體映射字體圖標
   * 所有 UI 元素保持一致的設計風格

3. **佈局**
   * 使用 `Grid`, `VStack`/`HStack` 進行佈局
   * 上下顯示區
   * 極簡入口

## 技術架構

1. **MVVM 架構**
   * Model: 數據模型和業務邏輯
   * View: UI 呈現
   * ViewModel: 連接 Model 和 View，處理業務邏輯

2. **SwiftUI 實現**
   * 使用 SwiftUI 框架
   * 自定義視圖和控件
   * 聲明式 UI

3. **依賴管理**
   * RevenueCat/purchases-ios (必需)
   * 數學解析庫 (優先使用第三方庫)
   * CocoaLumberjack (可選)

## 開發約束

1. **嚴格遵循 PRD 定義的功能範圍**
   * 不進行任何功能性增刪
   * 開發過程中需在程式碼中添加清晰註解

2. **UI 實現**
   * 必須使用用戶提供的自定義 TTF 字體文件
   * 必須使用 `AppIcons` 結構體進行圖標和數字映射

3. **開發方式**
   * 優先考慮使用簡單直接的開發方式
   * 目標是快速交付 MVP

4. **平台支持**
   * 僅限 iPhone
   * iOS 15.0+

## 未來考量 (MVP 階段不實現)

* 計算引擎優化
* 複雜動畫/交互
* iPad 支持
* 科學計算
* Widgets
* Watch App
* 更多主題/自定義
* 測試覆蓋率提升
