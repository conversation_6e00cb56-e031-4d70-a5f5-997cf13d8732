---
description: 
globs: 
alwaysApply: false
---
# 極簡計算機 iOS App - 專案結構指南

## 專案概述

這是一款基於 SwiftUI 的極簡主義 iOS 計算機應用程式，遵循 MVVM 架構設計。目前已完成的版本包含基本計算功能、歷史記錄和設定頁面，使用 TabView 實現垂直分頁滾動視圖（替換了原先的 ScrollView 方案），並採用自定義字體和圖標系統。專案整合了 AutoInch 框架以實現各種 iPhone 尺寸的精確等比例適配。

## 核心架構

專案嚴格遵循 MVVM (Model-View-ViewModel) 架構:

- **Model**: 定義數據結構和業務類型
- **View**: 負責 UI 呈現，僅通過 ViewModel 獲取和顯示數據
- **ViewModel**: 連接 Model 和 View，處理業務邏輯和狀態管理
- **Services**: 提供核心服務功能，如計算引擎和數據儲存
- **Utilities**: 通用工具類，如顏色轉換和字體加載

## 目錄結構

```
MinimalistCalculator/
├── MinimalistCalculatorApp.swift  # 應用程式入口點
├── ContentView.swift              # 主內容視圖
├── Model/                         # 數據模型
│   ├── ButtonType.swift           # 計算器按鈕類型
│   └── HistoryEntry.swift         # 歷史記錄項目模型
├── View/                          # UI 視圖組件
│   ├── MainPagingView.swift       # 垂直分頁滾動視圖 (TabView)
│   ├── CalculatorView.swift       # 計算器主視圖
│   ├── DisplayAreaView.swift      # 計算器顯示區域
│   ├── CalculatorButtonView.swift # 計算器按鈕視圖
│   ├── HistoryView.swift          # 歷史記錄視圖
│   ├── HistoryEntryRowView.swift  # 歷史記錄項目行視圖
│   └── SettingsPageView.swift     # 設定頁面視圖
├── ViewModel/                     # 視圖模型
│   └── CalculatorViewModel.swift  # 計算器視圖模型
├── Services/                      # 服務層
│   ├── CalculatorEngine.swift     # 計算引擎服務
│   └── HistoryRepository.swift    # 歷史記錄存儲服務
├── Utilities/                     # 工具類
│   ├── AppIcons.swift             # 自定義圖標字體映射
│   ├── AppIconsSymbol.swift       # 圖標符號工具類
│   ├── FontLoader.swift           # 自定義字體加載工具
│   └── HexColor.swift             # 十六進制顏色工具
├── Resources/                     # 資源文件
│   └── Fonts/                     # 自定義字體文件
└── Assets.xcassets/               # 圖片資源
    ├── AccentColor.colorset/      # 強調色資源
    └── AppIcon.appiconset/        # 應用圖標資源
```

## 主要文件說明

### 入口點

- [MinimalistCalculatorApp.swift](mdc:MinimalistCalculator/MinimalistCalculatorApp.swift): 應用程式入口點
  * 初始化自定義字體
  * 設置環境值和全局依賴
  * 設定主畫面為 ContentView

- [ContentView.swift](mdc:MinimalistCalculator/ContentView.swift): 主內容視圖
  * 作為一個簡單的容器視圖
  * 包裝並初始化 MainPagingView

### 視圖層 (View)

- [MainPagingView.swift](mdc:MinimalistCalculator/View/MainPagingView.swift): 垂直分頁滾動視圖
  * 使用 **TabView** 實現垂直分頁滾動行為 (早期版本使用 ScrollView)
  * 配置 `.tabViewStyle(.page(indexDisplayMode: .never))` 和 `.pageOrientation(.vertical)` 實現垂直滾動
  * 管理計算器視圖、歷史記錄視圖和設定頁面之間的切換
  * 提供分頁指示器和頁面轉換動畫

- [CalculatorView.swift](mdc:MinimalistCalculator/View/CalculatorView.swift): 計算器主視圖
  * 顯示計算器按鈕網格
  * 使用 GridLayout 排列按鈕
  * 處理按鈕點擊事件並傳遞給 ViewModel
  * 使用 AutoInch 框架適配不同尺寸的 iPhone

- [DisplayAreaView.swift](mdc:MinimalistCalculator/View/DisplayAreaView.swift): 計算器顯示區域
  * 顯示當前輸入和計算結果
  * 處理文本縮放以適應長表達式
  * 顯示錯誤信息和狀態指示
  * 使用 AutoInch 框架計算適當的字體大小

- [CalculatorButtonView.swift](mdc:MinimalistCalculator/View/CalculatorButtonView.swift): 計算器按鈕視圖
  * 根據按鈕類型渲染不同樣式的按鈕
  * 處理按鈕點擊動畫效果
  * 支持不同按鈕類型的視覺樣式
  * 使用 AutoInch 框架計算適當的按鈕尺寸和間距

- [HistoryView.swift](mdc:MinimalistCalculator/View/HistoryView.swift): 歷史記錄視圖
  * 顯示計算歷史記錄列表
  * 處理歷史記錄的選擇和清除
  * 提供空記錄狀態的處理

- [HistoryEntryRowView.swift](mdc:MinimalistCalculator/View/HistoryEntryRowView.swift): 歷史記錄項目行視圖
  * 渲染單個歷史記錄條目
  * 顯示表達式、結果和時間
  * 處理滑動刪除功能

- [SettingsPageView.swift](mdc:MinimalistCalculator/View/SettingsPageView.swift): 設定頁面視圖
  * 提供應用程式設定選項
  * 處理主題切換、通知設定等功能
  * 分組顯示設定項目

### 視圖模型層 (ViewModel)

- [CalculatorViewModel.swift](mdc:MinimalistCalculator/ViewModel/CalculatorViewModel.swift): 計算器視圖模型
  * 處理計算邏輯和狀態管理
  * 維護當前輸入和顯示狀態
  * 管理歷史記錄
  * 處理錯誤和異常情況
  * 提供反應式數據綁定

### 模型層 (Model)

- [ButtonType.swift](mdc:MinimalistCalculator/Model/ButtonType.swift): 計算器按鈕類型
  * 定義按鈕類型枚舉
  * 為每種按鈕類型提供視覺屬性
  * 包含按鈕行為和功能分類

- [HistoryEntry.swift](mdc:MinimalistCalculator/Model/HistoryEntry.swift): 歷史記錄項目模型
  * 定義歷史記錄數據結構
  * 支持 Codable 以便持久化
  * 包含表達式、結果和時間戳

### 服務層 (Services)

- [CalculatorEngine.swift](mdc:MinimalistCalculator/Services/CalculatorEngine.swift): 計算引擎服務
  * 提供表達式解析和計算功能
  * 處理數學運算和優先級
  * 提供錯誤處理和結果格式化

- [HistoryRepository.swift](mdc:MinimalistCalculator/Services/HistoryRepository.swift): 歷史記錄存儲服務
  * 管理歷史記錄的持久化
  * 提供保存、讀取和清除功能
  * 使用 UserDefaults 進行數據存儲

### 工具類 (Utilities)

- [AppIcons.swift](mdc:MinimalistCalculator/Utilities/AppIcons.swift): 自定義圖標字體映射
  * 定義應用圖標常量
  * 將圖標代碼映射到字體字符

- [AppIconsSymbol.swift](mdc:MinimalistCalculator/Utilities/AppIconsSymbol.swift): 圖標符號工具類
  * 提供圖標渲染功能
  * 支持不同尺寸和顏色的圖標

- [FontLoader.swift](mdc:MinimalistCalculator/Utilities/FontLoader.swift): 自定義字體加載工具
  * 管理自定義字體的註冊和加載
  * 提供字體名稱和樣式常量

- [HexColor.swift](mdc:MinimalistCalculator/Utilities/HexColor.swift): 十六進制顏色工具
  * 提供從十六進制字符串創建顏色的功能
  * 支持不同顏色格式轉換

## 依賴框架

### AutoInch

專案引入了 [AutoInch](mdc:https:/github.com/lixiang1994/AutoInch) 框架用於適配不同尺寸的 iPhone 螢幕：

* **目的**：實現各種 iPhone 尺寸的精確等比例適配
* **使用方式**：通過數值後的 `.auto()` 方法自動計算適合當前設備的尺寸
* **應用場景**：
  * 字體大小：`font = .systemFont(ofSize: 20.auto(), weight: .medium)`
  * 邊距：`make.left.right.equalToSuperview().inset(15.auto())`
  * 間距：`make.top.equalTo(16.auto())`
  * 圓角：`cornerRadius = 6.auto()`

* **優點**：
  * 一次適配，全尺寸支援
  * 精確等比例計算
  * 代碼簡潔易讀
  * 支持 Storyboard/Xib 適配

## 模塊依賴關係

```
View ──────> ViewModel ──────> Model
  │             │               
  │             v               
  │         Services          
  v             │            
Utilities <─────┘           
```

* View 依賴於 ViewModel 獲取數據和狀態
* ViewModel 依賴於 Model 和 Services
* Services 可能使用 Utilities 進行輔助功能
* View 可以直接使用 Utilities 進行 UI 渲染

## 架構改進

### ScrollView 到 TabView 的遷移

專案最初使用 ScrollView 實現垂直分頁滾動，後來改為使用 TabView：

* **原因**：
  * TabView 提供更流暢的分頁體驗
  * 內置的頁面指示器功能
  * 更好的性能和記憶體管理
  * 系統原生分頁行為

* **實現方式**：
  ```swift
  TabView(selection: $viewModel.currentPage) {
      CalculatorView(viewModel: viewModel)
          .tag(0)
      
      HistoryView(viewModel: viewModel)
          .tag(1)
      
      SettingsPageView(viewModel: viewModel)
          .tag(2)
  }
  .tabViewStyle(.page(indexDisplayMode: .never))
  .pageOrientation(.vertical)
  ```

## 擴展指南

### 添加新視圖

1. 在 `View/` 目錄中創建新的 SwiftUI 視圖文件
2. 遵循現有的命名約定 (`XxxView.swift`)
3. 使用 AutoInch 框架進行尺寸適配（`.auto()`方法）
4. 使用 HexColor 和 AppIcons 保持視覺一致性
5. 如需數據，通過 ViewModel 獲取

### 添加新功能

1. 評估該功能應該放在哪個層級:
   * UI 相關功能應放在 View 層
   * 業務邏輯應放在 ViewModel 層
   * 核心服務應放在 Services 層
   * 通用工具應放在 Utilities 層

2. 遵循現有架構和代碼風格:
   * 使用清晰的命名約定
   * 添加適當的註釋和 MARK 標記
   * 確保新功能與現有功能協調一致
   * 使用 AutoInch 框架處理尺寸適配

### 代碼風格規範

1. **命名規範**
   * 使用駝峰式命名法（小駝峰和大駝峰）
   * 類型名稱使用大駝峰 (PascalCase)
   * 變量和函數名稱使用小駝峰 (camelCase)
   * 常量使用小駝峰，但可全部大寫用下劃線分隔

2. **代碼組織**
   * 使用 MARK 標記區分不同功能區域
   * 相關功能應放在一起
   * 公共 API 放在前面，私有實現放在後面

3. **註釋規範**
   * 為公共 API 添加文檔註釋
   * 為複雜邏輯添加解釋性註釋
   * 使用 TODO 和 FIXME 標記待處理項目

## 相關資源和工具

* SwiftUI 官方文檔
* Swift Package Manager 依賴管理
* [AutoInch 框架](mdc:https:/github.com/lixiang1994/AutoInch)
* Xcode 13+ 開發環境
* iOS 15+ 目標部署平台

## 注意事項與限制

1. 專案目前集成 AutoInch 框架用於尺寸適配，新功能開發需使用此框架
2. 專案採用 TabView 進行垂直分頁，不要嘗試改回 ScrollView 實現
3. 專案目標為 iOS 15+，避免使用僅在更高版本可用的 API
4. 自定義字體和圖標系統需要額外的資源加載，注意性能影響
