---
description: 
globs: 
alwaysApply: false
---
# 下一建構階段：垂直分頁滾動視圖

## 專案目標

引入一個新的頂層容器視圖，該視圖以兩個垂直滾動、全頁且具有吸附（snapping）效果的區塊來呈現內容。第一頁包含現有的主要計算器視圖內容，第二頁是一個設定/資訊列表。

## 目標語言/框架

* SwiftUI

## 主要需求

1. **新的容器視圖：** 建立一個主視圖 (`MainPagingView`) 來管理垂直滾動。
2. **垂直滾動：** 實作頁面之間的垂直滾動。
3. **全頁與吸附：** 每個頁面必須佔滿螢幕高度，滾動應能清晰地吸附在頁面之間。
4. **頁面 1：**
   * 整合目前的計算器視圖 [CalculatorView.swift](mdc:MinimalistCalculator/View/CalculatorView.swift)。
   * 此視圖是*第一頁*（頂部）。
   * 在此頁面下方疊加一個「向下滾動」按鈕，用於導航至頁面 2。
5. **頁面 2：**
   * 為第二頁（底部）建立一個*新的*視圖 (`SettingsPageView`)。
   * **內容：** 以列表形式顯示「History」、「Dark Mode」、「About」。
   * **導航：** 在頂部包含一個「向上滾動」按鈕，用於返回頁面 1。
   * **頁腳：** 在最下方包含兩個社群媒體圖示按鈕。
6. **修改現有視圖：**
   * 從計算器視圖中，移除原本觸發 `showingHistory` 狀態變數的 `Button`。頁面 2 上的「History」選項將成為新的進入點。

## 實作細節

### 1. 建立容器視圖 (`MainPagingView.swift`)

* 使用 `GeometryReader` 獲取螢幕高度以實現全頁視圖。
* 使用 `ScrollView(.vertical)` 作為主要的容器。
* 對 `ScrollView` 套用 `.scrollTargetBehavior(.paging)` 以實現吸附效果。
* 在 `ScrollView` 內部，使用 `VStack` 來容納兩個頁面視圖。
* 使用 `ScrollViewReader` 來啟用透過按鈕進行程式化滾動。

### 2. 建立設定頁面視圖 (`SettingsPageView.swift`)

* 建立一個新的 SwiftUI View 檔案。
* 接受一個閉包 (`scrollToTopAction`) 來觸發滾動回頁面 1。
* 使用 `VStack` 作為主要佈局。
* 在頂部添加「向上滾動」按鈕，呼叫 `scrollToTopAction`。
* 使用 `List` 或 `VStack` 來放置設定項目（"History", "Dark Mode", "About"）。
* 使用 `Spacer()` 將社群圖示推到底部。
* 添加一個 `HStack` 來容納社群媒體按鈕。

### 3. 更新主 App 入口點

* 修改 [ContentView.swift](mdc:MinimalistCalculator/ContentView.swift) 或 [MinimalistCalculatorApp.swift](mdc:MinimalistCalculator/MinimalistCalculatorApp.swift)，使其顯示 `MainPagingView()` 作為主視圖。

### 4. 修改現有視圖 (計算器視圖)

* 修改 [CalculatorView.swift](mdc:MinimalistCalculator/View/CalculatorView.swift)，移除原有的歷史記錄按鈕。
* 確保此視圖能夠正確地整合到 `MainPagingView` 中。

## 注意事項

* 吸附效果的具體實作可能需要根據測試進行微調，特別是圍繞安全區域（safe areas）。`.scrollTargetBehavior(.paging)` 是首選的現代方法。
* 確保為滾動按鈕和社群媒體連結使用適當的圖示（使用 AppIcons 和自定義字體）。
* "History"、"Dark Mode" 和 "About" 的操作需要根據其預期功能來實作。
* `SettingsPageView` 的樣式（顏色、字體、間距）必須遵循極簡主義設計風格。
* 考慮可訪問性/輔助使用（accessibility）（例如，按鈕標籤）。
* 在 `SettingsPageView` 中，使用 `VStack` 配合自訂的設定行視圖和 `Divider` 可能比 `List` 更容易實現精確的佈局和背景控制。
* 注意安全區域，特別是在 `SettingsPageView` 的頂部返回按鈕和底部社群圖示的位置。

## 與 PRD 的關係

此開發階段實現了 PRD 中定義的核心功能，特別是：

1. 保持極簡主義設計風格
2. 使用自定義 TTF 字體和 AppIcons 結構體渲染界面元素
3. 提供基本計算功能和歷史記錄
4. 遵循 MVVM 架構設計

下一階段的開發將繼續遵循 PRD 中的設計原則和功能需求，確保應用程式的一致性和完整性。
