---
description: 
globs: 
alwaysApply: false
---
# 極簡計算機 iOS App - 數據流規範

## 架構概述

本專案採用 MVVM (Model-View-ViewModel) 架構，確保數據流的清晰和可維護性。數據流遵循單向流動原則，從 Model 到 ViewModel 再到 View，反向通過事件和綁定傳遞。

```
┌─────────┐    ┌───────────┐    ┌─────────┐
│  Model  │ -> │ ViewModel │ -> │  View   │
└─────────┘    └───────────┘    └─────────┘
     ^               ^               │
     │               │               │
     └───────────────┴───────────────┘
           (事件和狀態更新)
```

## 數據流動模式

### 1. 單向數據流

本專案採用嚴格的單向數據流模式，確保數據流動清晰可追蹤：

* 數據源頭：Model 層或 Services 層
* 數據處理：ViewModel 層
* 數據顯示：View 層
* 用戶交互：從 View 通過事件傳遞回 ViewModel

```
Data Source (Model/Services) → Processing (ViewModel) → Display (View)
                                     ↑                       |
                                     └─── User Interaction ──┘
```

### 2. 發布-訂閱模式

使用 SwiftUI 的 `@Published` 屬性和 Combine 框架實現響應式數據流：

* ViewModel 中的 `@Published` 屬性發布狀態變更
* View 通過 `@ObservedObject` 或 `@StateObject` 訂閱這些變更
* 狀態變更自動觸發 View 重新渲染

## TabView 分頁數據流

### 1. 頁面狀態管理

使用 TabView 管理垂直分頁視圖時的數據流：

* **頁面選擇狀態**: 使用 ViewModel 中的 `@Published var currentPage: Int` 追蹤和控制當前頁面
* **頁面觀察**: TabView 通過 `selection` 參數綁定到 currentPage 狀態
* **程式化切換**: 通過修改 `currentPage` 值實現程式化頁面切換

```swift
// ViewModel 中定義當前頁面狀態
@Published var currentPage: Int = 0

// View 中綁定 TabView 到頁面狀態
TabView(selection: $viewModel.currentPage) {
    CalculatorView(viewModel: viewModel)
        .tag(0)
    HistoryView(viewModel: viewModel)
        .tag(1)
    SettingsPageView(viewModel: viewModel)
        .tag(2)
}
```

### 2. 頁面切換事件

* 用戶滑動切換頁面時，SwiftUI 自動更新 `currentPage` 值
* ViewModel 可以監聽 `currentPage` 變化，執行相應的業務邏輯
* 可以使用 `.onChange(of: viewModel.currentPage)` 監聽頁面切換事件

## 核心數據流

### 1. 計算器數據流

* **輸入流程**
  * 用戶在 [CalculatorView.swift](mdc:MinimalistCalculator/View/CalculatorView.swift) 中點擊按鈕
  * 按鈕點擊事件傳遞到 [CalculatorViewModel.swift](mdc:MinimalistCalculator/ViewModel/CalculatorViewModel.swift)
  * ViewModel 根據 [ButtonType.swift](mdc:MinimalistCalculator/Model/ButtonType.swift) 處理輸入
  * ViewModel 更新狀態 (如 `currentInput`, `displayText`)
  * 更新後的狀態通過 `@Published` 屬性反映到 View

* **計算流程**
  * 當用戶點擊 "=" 按鈕時，觸發計算
  * ViewModel 調用 [CalculatorEngine.swift](mdc:MinimalistCalculator/Services/CalculatorEngine.swift) 進行計算
  * 計算結果更新到 ViewModel 的狀態
  * 同時生成一個新的 [HistoryEntry.swift](mdc:MinimalistCalculator/Model/HistoryEntry.swift) 對象
  * 計算記錄保存到 [HistoryRepository.swift](mdc:MinimalistCalculator/Services/HistoryRepository.swift)
  * 計算結果通過 `@Published` 屬性反映到 DisplayAreaView 顯示

* **錯誤處理流程**
  * 計算過程中的錯誤由 CalculatorEngine 返回
  * ViewModel 捕獲錯誤並更新 `errorMessage` 狀態
  * 錯誤信息通過 `@Published` 屬性反映到 DisplayAreaView 顯示
  * 用戶可點擊 AC 按鈕清除錯誤狀態

### 2. 歷史記錄數據流

* **讀取歷史**
  * ViewModel 初始化時從 HistoryRepository 讀取歷史記錄
  * 歷史記錄轉換為 `[HistoryEntry]` 數組
  * 記錄作為 `@Published` 屬性提供給 [HistoryView.swift](mdc:MinimalistCalculator/View/HistoryView.swift)
  * HistoryView 使用 ForEach 渲染歷史條目列表

* **使用歷史記錄**
  * 用戶點擊歷史記錄項目
  * 點擊事件傳遞到 ViewModel
  * ViewModel 更新當前輸入為選中的歷史記錄
  * ViewModel 設置 `currentPage = 0` 切換回計算器視圖
  * 更新後的狀態反映到 CalculatorView

* **清除歷史**
  * 用戶點擊清除按鈕
  * ViewModel 調用 HistoryRepository.clearHistory()
  * HistoryRepository 清除 UserDefaults 中的歷史數據
  * ViewModel 更新 `historyEntries` 為空數組
  * 更新後的空歷史列表反映到 HistoryView

### 3. 設置頁面數據流

* **讀取設置**
  * ViewModel 初始化時從 UserDefaults 讀取用戶設置
  * 設置作為 `@Published` 屬性提供給 [SettingsPageView.swift](mdc:MinimalistCalculator/View/SettingsPageView.swift)

* **更新設置**
  * 用戶在設置頁面更改選項
  * 更改事件傳遞到 ViewModel
  * ViewModel 更新相應設置並保存到 UserDefaults
  * 設置更改可能觸發全局環境值變更（如主題）
  * 設置變更立即應用到整個應用

## 自適應佈局數據流

### 1. AutoInch 尺寸適配

專案使用 [AutoInch](mdc:https:/github.com/lixiang1994/AutoInch) 框架實現各種 iPhone 尺寸的精確等比例適配：

* **尺寸轉換流程**
  * 設計時基於參考設備（例如 iPhone 13）設定基準尺寸
  * 在代碼中使用 `.auto()` 方法轉換固定尺寸為適應性尺寸
  * AutoInch 框架根據當前設備與基準設備的比例計算實際尺寸
  * 計算結果用於設置 UI 元素尺寸、間距和字體大小

* **實現原理**
  * 通過擴展 Int、Double、CGFloat 等類型添加 `.auto()` 方法
  * 內部使用設備屏幕寬度比例進行等比例縮放計算
  * 對於特殊設備提供自定義調整系數

### 2. 響應式佈局

* **GeometryReader 與 AutoInch 結合**
  * 對於複雜佈局，可以結合 GeometryReader 獲取容器尺寸
  * 使用容器尺寸的百分比結合 AutoInch 適配 
  * 例如：`width: geo.size.width * 0.9, height: 50.auto()`

* **適配數據傳遞**
  * 視圖可以通過環境值獲取設備類型和尺寸信息
  * ViewModel 可以根據設備類型調整數據處理邏輯

## 數據模型設計

### 1. 核心模型類型

* **[ButtonType.swift](mdc:MinimalistCalculator/Model/ButtonType.swift)**
  * 枚舉類型，定義計算器按鈕類型和行為
  * 每種按鈕類型包含顯示文本、顏色等視覺屬性
  * 提供計算函數，用於處理按鈕點擊後的行為

```swift
enum ButtonType {
    case number(Int)
    case decimal
    case equals
    case add, subtract, multiply, divide
    case clear, allClear
    case percent, negate
    // ... 其他類型
    
    var displayText: String { ... }
    var backgroundColor: Color { ... }
    var foregroundColor: Color { ... }
}
```

* **[HistoryEntry.swift](mdc:MinimalistCalculator/Model/HistoryEntry.swift)**
  * 結構體，定義歷史記錄條目的數據結構
  * 包含表達式、結果和時間戳
  * 實現 Codable 協議以支持序列化

```swift
struct HistoryEntry: Identifiable, Codable {
    var id: UUID = UUID()
    var expression: String
    var result: String
    var timestamp: Date
    
    // 可能還有其他輔助方法
}
```

### 2. 模型不可變性原則

* 模型應設計為不可變的 (immutable)，使用 `let` 而非 `var`
* 狀態變更應通過創建新的模型實例而非修改現有實例
* 這確保數據流的可預測性和追蹤性

## 狀態管理

### 1. ViewModel 狀態

[CalculatorViewModel.swift](mdc:MinimalistCalculator/ViewModel/CalculatorViewModel.swift) 包含以下關鍵狀態：

* `currentInput`: 當前輸入的表達式
* `displayText`: 顯示在計算器上的文本
* `historyEntries`: 歷史記錄列表
* `showingHistory`: 是否顯示歷史記錄視圖
* `currentPage`: 當前顯示的頁面索引（用於 TabView）
* `errorMessage`: 計算錯誤信息
* `settings`: 用戶設置

這些狀態使用 `@Published` 屬性包裝，確保 View 能夠響應狀態變化。

### 2. SwiftUI 狀態屬性使用指南

* **@State**: 用於簡單的視圖內部狀態
  * 適用於暫時性、局部的 UI 狀態
  * 例如：按鈕是否被按下、輸入字段臨時文本

* **@Binding**: 用於從父視圖傳遞可修改的狀態
  * 允許子視圖修改父視圖的狀態
  * 例如：CalculatorButtonView 接收並可能修改 CalculatorView 的狀態

* **@ObservedObject**: 用於觀察外部對象的狀態變化
  * 適用於從 ViewModel 獲取狀態
  * 例如：View 觀察 ViewModel 的狀態變化

* **@StateObject**: 用於擁有和管理 ViewModel 生命周期
  * 確保 ViewModel 的生命周期與視圖一致
  * 例如：主視圖創建和擁有 CalculatorViewModel

* **@EnvironmentObject**: 用於全局共享狀態
  * 適用於需要在整個應用中共享的狀態
  * 例如：主題設置、用戶偏好

### 3. TabView 的狀態管理

* **視圖內狀態管理**
  * 使用 `@State private var selectedTab: Int = 0` 在單個視圖內管理 TabView
  * TabView 通過 `TabView(selection: $selectedTab)` 綁定

* **全局狀態管理**
  * 如果需要從 ViewModel 控制頁面切換，使用 `@Published var currentPage: Int`
  * 在 ViewModel 中提供切換頁面的方法
  * 例如：`func switchToCalculator() { currentPage = 0 }`

### 4. 狀態更新最佳實踐

* **批量更新**: 相關狀態應一起更新，避免多次觸發 UI 刷新
* **有限狀態機**: 對於複雜的狀態轉換，考慮使用有限狀態機模式
* **衍生狀態**: 使用計算屬性而非存儲冗餘狀態
* **狀態隔離**: 盡量將狀態限制在最小範圍內

## 數據持久化

### 1. 歷史記錄持久化

* **[HistoryRepository.swift](mdc:MinimalistCalculator/Services/HistoryRepository.swift)** 負責歷史記錄的存儲和檢索
  * 使用 `UserDefaults` 進行簡單持久化
  * 歷史記錄序列化為 JSON 存儲
  * 提供 CRUD 操作方法

```swift
func saveHistory(_ entries: [HistoryEntry]) {
    // 序列化為 JSON 並存儲到 UserDefaults
}

func loadHistory() -> [HistoryEntry] {
    // 從 UserDefaults 讀取 JSON 並反序列化
}

func clearHistory() {
    // 從 UserDefaults 中刪除歷史記錄
}
```

* **持久化時機**
  * 每次計算完成後自動保存到歷史記錄
  * 用戶手動清除歷史時立即更新持久存儲
  * 應用進入後台時確保所有更改已保存

### 2. 用戶設置持久化

* **設置類型**
  * 主題選擇 (淺色/深色)
  * 聲音開關
  * 保留小數位數
  * 其他用戶偏好

* **設置存儲**
  * 使用 UserDefaults 存儲設置
  * 設置變更應立即持久化
  * 應用啟動時加載設置

## 非同步操作處理

### 1. 非同步數據加載

* 使用 Swift 的 `async/await` 進行非同步操作
* 非同步操作應在 ViewModel 或 Service 層處理
* 使用 `@MainActor` 確保 UI 更新在主線程進行

```swift
@MainActor
func loadDataAsync() async {
    isLoading = true
    do {
        let result = try await service.fetchData()
        data = result
        errorMessage = nil
    } catch {
        errorMessage = error.localizedDescription
    }
    isLoading = false
}
```

### 2. 長時間運算

* 對於複雜計算，應考慮在後台線程執行
* 使用 `Task` 或 `DispatchQueue` 避免阻塞 UI
* 計算完成後，在主線程更新 UI

## 錯誤處理策略

### 1. 錯誤分類

* **計算錯誤**: 除以零、無效表達式等
* **數據錯誤**: 無法加載或保存數據
* **系統錯誤**: 資源不足等

### 2. 錯誤處理流程

* 錯誤應在發生處就近捕獲
* 錯誤信息應通過 ViewModel 的狀態傳遞到 UI
* UI 應顯示用戶友好的錯誤信息
* 提供重試或恢復機制

## 擴展指南

### 1. 添加新的數據模型

添加新的數據模型時，應遵循以下步驟：

1. 在 Model 目錄中創建新的模型文件
2. 定義明確的數據結構和屬性
3. 實現 Codable 協議以支持序列化（如需持久化）
4. 如有必要，實現 Identifiable 協議以支持 SwiftUI 列表

### 2. 擴展現有數據流

擴展現有功能時，應：

1. 確保遵循單向數據流原則
2. 在合適的層級添加新的狀態或方法
3. 使用適當的 SwiftUI 狀態屬性傳遞數據
4. 保持狀態更新的一致性和原子性
5. 考慮如何與 TabView 分頁視圖和 AutoInch 框架集成

### 3. 添加新的頁面到垂直 TabView

添加新頁面到 TabView 時，遵循以下步驟：

1. 在 ViewModel 中擴展頁面標識常量
   ```swift
   // 在 ViewModel 中
   static let PAGE_CALCULATOR = 0
   static let PAGE_HISTORY = 1
   static let PAGE_SETTINGS = 2
   static let PAGE_NEW_FEATURE = 3  // 新頁面
   ```

2. 在 MainPagingView 的 TabView 中添加新頁面
   ```swift
   TabView(selection: $viewModel.currentPage) {
       // 現有頁面...
       
       NewFeatureView(viewModel: viewModel)
           .tag(CalculatorViewModel.PAGE_NEW_FEATURE)
   }
   ```

3. 添加導航功能
   ```swift
   // 在 ViewModel 中
   func navigateToNewFeature() {
       currentPage = PAGE_NEW_FEATURE
   }
   ```

### 4. 添加新的功能模塊

添加新的功能模塊時，應：

1. 評估是否需要新的 ViewModel
2. 設計清晰的數據流動路徑
3. 確定與現有模塊的交互方式
4. 選擇適當的狀態管理和數據持久化策略
5. 使用 AutoInch 框架進行尺寸適配

## 測試策略

### 1. 單元測試

* **ViewModel 測試**
  * 測試狀態更新邏輯
  * 模擬用戶輸入序列
  * 驗證計算結果和錯誤處理
  * 測試頁面切換邏輯

* **Model 測試**
  * 測試數據結構和方法
  * 測試序列化和反序列化

* **Service 測試**
  * 測試計算引擎的各種表達式
  * 測試數據持久化和檢索

### 2. UI 測試

* 測試用戶交互流程
* 驗證 UI 元素的正確顯示
* 測試頁面滑動和 TabView 的分頁功能
* 測試動畫和轉場效果

## 性能優化

* **懶加載**: 延遲加載不立即需要的數據
* **批量更新**: 合併多個狀態更新為一次更新
* **記憶化**: 緩存複雜計算的結果
* **資源管理**: 適時釋放不再需要的資源
* **頁面預加載**: 僅在需要時加載 TabView 中的頁面內容
