---
description: 
globs: 
alwaysApply: false
---
# MathParser框架指南

## 框架簡介

MathParser是一個用於解析和計算數學表達式的Swift框架，在本專案中用於解決計算精度問題，特別是處理連續除法和複雜乘除法組合的情況。

## 核心文件

- [CalculatorEngine.swift](mdc:MinimalistCalculator/Services/CalculatorEngine.swift): 整合MathParser框架的計算引擎
- [CalculatorViewModel.swift](mdc:MinimalistCalculator/ViewModel/CalculatorViewModel.swift): 處理使用者界面邏輯和計算請求

## 框架關鍵功能

### 高精度表達式解析

MathParser提供了強大的表達式解析能力，支持各種常見數學運算：

```swift
// 標準化表達式（替換特殊運算符）
let standardizedExpression = processedExpression
    .replacingOccurrences(of: "×", with: "*")
    .replacingOccurrences(of: "÷", with: "/")

// 使用MathParser庫解析表達式
let result = parser.parseResult(standardizedExpression)
```

### 隱式乘法支持

可以識別和處理無乘號的乘法表達式，例如`2(3+4)`或`5x(3+2)`：

```swift
// 在初始化時啟用隱式乘法
init(enableImpliedMultiplication: Bool = true) {
    self.enableImpliedMultiplication = enableImpliedMultiplication
    self.parser = MathParser(enableImpliedMultiplication: enableImpliedMultiplication)
}
```

### 錯誤處理機制

提供詳細的錯誤報告和處理機制：

```swift
case .failure(let error):
    // 處理解析錯誤
    print("解析錯誤: \(error)")
    
    // 檢查是否為除零錯誤
    let errorString = String(describing: error)
    if errorString.contains("divide by zero") || errorString.contains("division by zero") {
        throw CalculationError.divisionByZero
    } else {
        // 嘗試使用備用計算方法
        if standardizedExpression.contains("*") && standardizedExpression.contains("/") {
            if let result = try? specialCaseCalculation(standardizedExpression) {
                print("使用特殊情況處理: \(result)")
                return result
            }
        }
        
        throw CalculationError.invalidExpression
    }
```

## 使用範例

### 基本表達式計算

```swift
// 計算簡單表達式
func calculateSimpleExpression(_ expression: String) -> Double? {
    try? calculatorEngine.calculate(expression)
}

// 使用範例
let result1 = calculateSimpleExpression("5*24/1000") // 返回 0.12
let result2 = calculateSimpleExpression("5*24/1000*5") // 返回 0.6
```

### 處理特殊情況

```swift
// 處理可能會有精度問題的表達式
func calculatePrecisionCritical(_ expression: String) -> String {
    do {
        let result = try calculatorEngine.calculate(expression)
        return calculatorEngine.formatResult(result)
    } catch {
        return "Error"
    }
}

// 使用範例
let formattedResult = calculatePrecisionCritical("5.1*24.75/1000.25") // 返回更精確的結果
```

## 主要優勢

1. **精度提升**: 解決了原生Double計算可能導致的精度丟失問題
2. **表達式解析能力**: 支持複雜表達式，無需自行實現解析邏輯
3. **易於整合**: 簡潔的API設計，易於整合到現有代碼中
4. **錯誤處理**: 提供詳細的錯誤資訊和處理機制
5. **支持隱式乘法**: 提升用戶體驗，允許更自然的表達式輸入方式

## 常見問題處理

### 計算精度問題

例如`5×24÷1000`應顯示為`0.12`而不是`0`：

```swift
// 正確解決方法
let result = parser.parseResult("5*24/1000")
if case .success(let evaluator) = result {
    let value = evaluator.value // 正確值為 0.12
    print(formatResult(value))
}
```

### 連續除法問題

例如`100÷5÷4`應正確計算為`5`而非`80`：

```swift
// 從左到右計算
// 100÷5 = 20, 20÷4 = 5
let result = parser.parseResult("100/5/4")
if case .success(let evaluator) = result {
    let value = evaluator.value // 正確值為 5
    print(formatResult(value))
}
```

## 最佳實踐

1. 始終使用框架的錯誤處理機制來捕獲和處理計算問題
2. 提供備用計算方法以應對框架可能無法處理的特殊情況
3. 結合Decimal類型進行結果格式化以維持高精度
4. 根據數值大小調整顯示的小數位數
5. 使用科學計數法顯示極小或極大的數值
6. 維護適當的錯誤消息以提升用戶體驗
