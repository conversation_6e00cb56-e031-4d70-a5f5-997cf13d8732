---
description: 
globs: 
alwaysApply: false
---
# 計算引擎優化指南

## 核心文件

計算器應用的核心計算邏輯位於 [CalculatorEngine.swift](mdc:MinimalistCalculator/Services/CalculatorEngine.swift)，使用者界面邏輯由 [CalculatorViewModel.swift](mdc:MinimalistCalculator/ViewModel/CalculatorViewModel.swift) 處理。

## MathParser框架整合

### 框架概述

本專案已整合MathParser框架來提升數學表達式解析和計算的精度。MathParser提供了以下關鍵功能：

- 高精度表達式解析與計算
- 支援隱式乘法（如 `2(3+4)` 等形式）
- 完善的錯誤處理機制
- 更準確的浮點數計算結果

### 框架使用方式

```swift
// 初始化MathParser
private let parser = MathParser(enableImpliedMultiplication: true)

// 使用MathParser解析表達式
let result = parser.parseResult(standardizedExpression)

switch result {
case .success(let evaluator):
    // 取得計算結果
    let value = evaluator.value
    
case .failure(let error):
    // 處理解析錯誤
    print("解析錯誤: \(error)")
}
```

## 高精度計算原則

### 使用 Decimal 而非 Double

為了避免浮點數精度問題，計算引擎使用 `Decimal` 類型進行所有數學運算：

```swift
// 推薦：使用 Decimal 進行高精度計算
let result = Decimal(string: "5.0")! * Decimal(string: "24.0")! / Decimal(string: "1000.0")!
// 轉換回 Double 僅用於顯示
let doubleResult = NSDecimalNumber(decimal: result).doubleValue
```

### 表達式解析與計算

計算引擎使用以下步驟處理表達式：
1. 預處理表達式（替換乘除符號、處理負數等）
2. 使用MathParser解析表達式並進行計算
3. 如果主要方法失敗，使用備用計算方法
4. 使用 `Decimal` 處理結果格式化

## 結果格式化策略

### 動態精度調整

根據數值大小動態調整顯示的小數位數：

```swift
/// 確定適合的小數位數
private func determinePrecision(for value: Double) -> Int {
    let absValue = abs(value)
    
    // 針對非常小的數值增加精度
    if absValue < 0.0001 {
        return 10
    } else if absValue < 0.01 {
        return 8
    } else if absValue < 1 {
        return 6
    } else if absValue < 1000 {
        return 4
    } else {
        return 2
    }
}
```

### 整數識別增強

使用多重檢查方式確保正確識別整數值：

```swift
extension Decimal {
    var isInteger: Bool {
        // 多重檢查，確保整數識別的準確性
        
        // 1. 檢查是否與取整值的差異非常小
        let doubleValue = NSDecimalNumber(decimal: self).doubleValue
        let roundedValue = doubleValue.rounded()
        let epsilon = 1e-10
        if abs(roundedValue - doubleValue) < epsilon {
            return true
        }
        
        // 2. 嘗試使用字符串表示法進行判斷
        let stringValue = NSDecimalNumber(decimal: self).stringValue
        if !stringValue.contains(".") {
            return true
        }
        
        // 3. 檢查小數部分是否為零（考慮科學計數法）
        if let decimalPoint = stringValue.firstIndex(of: ".") {
            let fractionalPart = stringValue[stringValue.index(after: decimalPoint)...]
            if fractionalPart.allSatisfy({ $0 == "0" }) {
                return true
            }
        }
        
        return false
    }
}
```

## 關鍵優化

### 特殊情況處理

針對MathParser可能無法完美處理的情況提供了備用計算方法：

```swift
/// 特殊情況計算 - 處理連續乘除法問題（備用方法）
private func specialCaseCalculation(_ expression: String) throws -> Double {
    // 分解表達式
    let tokens = expression.components(separatedBy: CharacterSet(charactersIn: "+-*/()"))
        .filter { !$0.isEmpty }
        .compactMap { Decimal(string: $0) }
    
    // 構建運算符列表
    var operators: [Character] = []
    for char in expression {
        if "+-*/".contains(char) {
            operators.append(char)
        }
    }
    
    // 從左到右計算
    if !tokens.isEmpty && tokens.count == operators.count + 1 {
        var result = tokens[0]
        
        for i in 0..<operators.count {
            let op = operators[i]
            let operand = tokens[i+1]
            
            switch op {
            case "*": result *= operand
            case "/": result /= operand
            case "+": result += operand
            case "-": result -= operand
            default: break
            }
        }
        
        return NSDecimalNumber(decimal: result).doubleValue
    }
    
    throw CalculationError.invalidExpression
}
```

### 科學計數法處理

對於極小或極大的數值使用科學計數法顯示：

```swift
/// 格式化科學計數法
private func formatScientificNotation(_ value: Double) -> String {
    let formatter = NumberFormatter()
    formatter.numberStyle = .scientific
    formatter.exponentSymbol = "e"
    formatter.minimumFractionDigits = 2
    formatter.maximumFractionDigits = 2
    
    if let formatted = formatter.string(from: NSNumber(value: value)) {
        return formatted
    }
    
    return String(format: "%.2e", value)
}
```

## 最佳實踐總結

1. 優先使用MathParser框架進行表達式解析和計算
2. 提供備用計算方法處理特殊情況
3. 使用Decimal類型保持計算精度
4. 根據數值範圍動態調整小數位數
5. 移除結果中多餘的尾部零
6. 使用多重檢查方法精確識別整數
7. 對極小或極大的數值使用科學計數法

