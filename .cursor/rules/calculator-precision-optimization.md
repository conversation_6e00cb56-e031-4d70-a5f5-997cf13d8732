# 計算器精度優化最佳實踐

## 問題描述

計算器在處理以下情況時存在精度問題：

1. 連續除法操作（例如 5×24/1000 顯示為 0 而非 0.12）
2. 複雜乘除法組合（例如 5×24/1000×5 結果不正確）
3. 小數點位數顯示不一致
4. 極小數值被錯誤地判斷為零

## 解決方案

### 1. 使用 Decimal 類型進行高精度計算

```swift
// 使用 Decimal 進行精確計算
let stringValue = result.stringValue
if let decimalValue = Decimal(string: stringValue) {
    return NSDecimalNumber(decimal: decimalValue).doubleValue
}
```

### 2. 特殊處理連續除法和複雜乘除法組合

```swift
// 特殊情況處理 - 連續除法問題
if expression.contains("*") && expression.contains("/") {
    // 使用 Decimal 類型來處理高精度計算
    let tokens = expression.components(separatedBy: CharacterSet(charactersIn: "+-*/()"))
        .filter { !$0.isEmpty }
        .compactMap { Decimal(string: $0) }
    
    // 按照運算符優先級處理表達式
    var operators: [Character] = []
    for char in expression {
        if "+-*/".contains(char) {
            operators.append(char)
        }
    }
    
    // 循環計算
    if !tokens.isEmpty && tokens.count == operators.count + 1 {
        var result = tokens[0]
        
        for i in 0..<operators.count {
            let op = operators[i]
            let operand = tokens[i+1]
            
            switch op {
            case "*":
                result *= operand
            case "/":
                result /= operand
            case "+":
                result += operand
            case "-":
                result -= operand
            default:
                break
            }
        }
        
        return NSDecimalNumber(decimal: result).doubleValue
    }
}
```

### 3. 改進整數判斷方法

```swift
extension Decimal {
    var isInteger: Bool {
        // 使用 NSDecimalNumber 的方法檢查是否為整數
        let doubleValue = NSDecimalNumber(decimal: self).doubleValue
        // 允許極小的誤差
        let epsilon = 1e-10
        return abs(doubleValue.rounded() - doubleValue) < epsilon
    }
}
```

### 4. 優化小數格式化邏輯

```swift
private func determinePrecision(for value: Double) -> Int {
    let absValue = abs(value)
    
    // 針對非常小的數值增加精度
    if absValue < 0.0001 {
        return 10
    } else if absValue < 0.01 {
        return 8
    } else if absValue < 1 {
        return 6
    } else if absValue < 1000 {
        return 4
    } else {
        return 2
    }
}
```

### 5. 處理極小數特殊情況

```swift
// 處理特殊情況：極小數可能被錯誤地認為是零
let doubleValue = decimalValue.doubleValue
if abs(doubleValue) < 1e-10 && doubleValue != 0 {
    // 保留極小數值，避免被當作零
    return doubleValue
}
```

## 進一步優化建議

1. **添加專業數學庫**：使用 DDMathParser 等專業數學表達式解析庫來提高計算精度。

2. **單元測試**：為各種邊緣情況添加單元測試，例如：
   - 連續除法：`5*24/1000` → `0.12`
   - 複合運算：`5*24/1000*5` → `0.6`
   - 極小數：`1/10000000` → `0.0000001`

3. **錯誤處理**：完善錯誤處理機制，提供更具體的錯誤訊息。

4. **用戶體驗**：在計算過程中顯示中間結果，讓用戶理解計算邏輯。

## 可能替代方案

如果上述方法仍無法完全解決問題，可以考慮：

1. 使用 NSDecimalNumber 的運算方法直接計算，而不是通過 NSExpression。
2. 實現自定義的數學表達式解析器，完全控制計算過程。
3. 將複雜計算卸載到後端或使用 WebAssembly 版本的高精度計算庫。

## 參考資料

- [Apple 文檔：Decimal](https://developer.apple.com/documentation/foundation/decimal)
- [Apple 文檔：NSDecimalNumber](https://developer.apple.com/documentation/foundation/nsdecimalnumber)
- [DDMathParser GitHub](https://github.com/davedelong/DDMathParser)
- [Swift 數值精度問題](https://www.swiftbysundell.com/articles/decimal-numbers-in-swift/) 