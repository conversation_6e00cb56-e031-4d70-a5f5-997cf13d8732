// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		FC0AC4852DD302AB0074788A /* AutoInch in Frameworks */ = {isa = PBXBuildFile; productRef = FC0AC4842DD302AB0074788A /* AutoInch */; };
		FC9867F32DD441AB0018E3B0 /* MathParser in Frameworks */ = {isa = PBXBuildFile; productRef = FC9867F22DD441AB0018E3B0 /* MathParser */; };
		FCD9F45B2DDEBD03001D12B1 /* RevenueCat in Frameworks */ = {isa = PBXBuildFile; productRef = FCD9F45A2DDEBD03001D12B1 /* RevenueCat */; };
		FCD9F45D2DDEBD03001D12B1 /* RevenueCatUI in Frameworks */ = {isa = PBXBuildFile; productRef = FCD9F45C2DDEBD03001D12B1 /* RevenueCatUI */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		FC16B23D2DDECE9B00C58BA8 /* Calculator Minimal.storekit */ = {isa = PBXFileReference; lastKnownFileType = text; path = "Calculator Minimal.storekit"; sourceTree = "<group>"; };
		FC16B2412DDECF2E00C58BA8 /* StoreKitTestCertificate.cer */ = {isa = PBXFileReference; lastKnownFileType = file; path = StoreKitTestCertificate.cer; sourceTree = "<group>"; };
		FC68BF7E2DAEADED00588A7F /* MinimalistCalculator.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MinimalistCalculator.app; sourceTree = BUILT_PRODUCTS_DIR; };
		FC68BF8C2DAEB0B700588A7F /* info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		FC68BF802DAEADED00588A7F /* MinimalistCalculator */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = MinimalistCalculator;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		FC68BF7B2DAEADED00588A7F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FCD9F45D2DDEBD03001D12B1 /* RevenueCatUI in Frameworks */,
				FC0AC4852DD302AB0074788A /* AutoInch in Frameworks */,
				FC9867F32DD441AB0018E3B0 /* MathParser in Frameworks */,
				FCD9F45B2DDEBD03001D12B1 /* RevenueCat in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		FC68BF752DAEADED00588A7F = {
			isa = PBXGroup;
			children = (
				FC16B23D2DDECE9B00C58BA8 /* Calculator Minimal.storekit */,
				FC16B2412DDECF2E00C58BA8 /* StoreKitTestCertificate.cer */,
				FC68BF8C2DAEB0B700588A7F /* info.plist */,
				FC68BF802DAEADED00588A7F /* MinimalistCalculator */,
				FCA4E2592DD1D3EF0067998A /* Frameworks */,
				FC68BF7F2DAEADED00588A7F /* Products */,
			);
			sourceTree = "<group>";
		};
		FC68BF7F2DAEADED00588A7F /* Products */ = {
			isa = PBXGroup;
			children = (
				FC68BF7E2DAEADED00588A7F /* MinimalistCalculator.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		FCA4E2592DD1D3EF0067998A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		FC68BF7D2DAEADED00588A7F /* MinimalistCalculator */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FC68BF892DAEADEF00588A7F /* Build configuration list for PBXNativeTarget "MinimalistCalculator" */;
			buildPhases = (
				FC68BF7A2DAEADED00588A7F /* Sources */,
				FC68BF7B2DAEADED00588A7F /* Frameworks */,
				FC68BF7C2DAEADED00588A7F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				FC68BF802DAEADED00588A7F /* MinimalistCalculator */,
			);
			name = MinimalistCalculator;
			packageProductDependencies = (
				FC0AC4842DD302AB0074788A /* AutoInch */,
				FC9867F22DD441AB0018E3B0 /* MathParser */,
				FCD9F45A2DDEBD03001D12B1 /* RevenueCat */,
				FCD9F45C2DDEBD03001D12B1 /* RevenueCatUI */,
			);
			productName = MinimalistCalculator;
			productReference = FC68BF7E2DAEADED00588A7F /* MinimalistCalculator.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		FC68BF762DAEADED00588A7F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					FC68BF7D2DAEADED00588A7F = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = FC68BF792DAEADED00588A7F /* Build configuration list for PBXProject "MinimalistCalculator" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hant",
				ja,
			);
			mainGroup = FC68BF752DAEADED00588A7F;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				FC0AC4832DD302AB0074788A /* XCRemoteSwiftPackageReference "AutoInch" */,
				FC9867F12DD441AB0018E3B0 /* XCRemoteSwiftPackageReference "swift-math-parser" */,
				FCD9F4592DDEBD03001D12B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = FC68BF7F2DAEADED00588A7F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				FC68BF7D2DAEADED00588A7F /* MinimalistCalculator */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		FC68BF7C2DAEADED00588A7F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		FC68BF7A2DAEADED00588A7F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		FC68BF872DAEADEF00588A7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		FC68BF882DAEADEF00588A7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		FC68BF8A2DAEADEF00588A7F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 22;
				DEVELOPMENT_TEAM = BJ3PBAQ57V;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = calc;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1.2;
				PRODUCT_BUNDLE_IDENTIFIER = com.minlsm.calculator;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		FC68BF8B2DAEADEF00588A7F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 22;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = BJ3PBAQ57V;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = calc;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1.2;
				PRODUCT_BUNDLE_IDENTIFIER = com.minlsm.calculator;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "Calculator App Store";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		FC68BF792DAEADED00588A7F /* Build configuration list for PBXProject "MinimalistCalculator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FC68BF872DAEADEF00588A7F /* Debug */,
				FC68BF882DAEADEF00588A7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FC68BF892DAEADEF00588A7F /* Build configuration list for PBXNativeTarget "MinimalistCalculator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FC68BF8A2DAEADEF00588A7F /* Debug */,
				FC68BF8B2DAEADEF00588A7F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		FC0AC4832DD302AB0074788A /* XCRemoteSwiftPackageReference "AutoInch" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/lixiang1994/AutoInch.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.6.0;
			};
		};
		FC9867F12DD441AB0018E3B0 /* XCRemoteSwiftPackageReference "swift-math-parser" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/bradhowes/swift-math-parser";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 3.7.3;
			};
		};
		FCD9F4592DDEBD03001D12B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/RevenueCat/purchases-ios-spm.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		FC0AC4842DD302AB0074788A /* AutoInch */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC0AC4832DD302AB0074788A /* XCRemoteSwiftPackageReference "AutoInch" */;
			productName = AutoInch;
		};
		FC9867F22DD441AB0018E3B0 /* MathParser */ = {
			isa = XCSwiftPackageProductDependency;
			package = FC9867F12DD441AB0018E3B0 /* XCRemoteSwiftPackageReference "swift-math-parser" */;
			productName = MathParser;
		};
		FCD9F45A2DDEBD03001D12B1 /* RevenueCat */ = {
			isa = XCSwiftPackageProductDependency;
			package = FCD9F4592DDEBD03001D12B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCat;
		};
		FCD9F45C2DDEBD03001D12B1 /* RevenueCatUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = FCD9F4592DDEBD03001D12B1 /* XCRemoteSwiftPackageReference "purchases-ios-spm" */;
			productName = RevenueCatUI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = FC68BF762DAEADED00588A7F /* Project object */;
}
