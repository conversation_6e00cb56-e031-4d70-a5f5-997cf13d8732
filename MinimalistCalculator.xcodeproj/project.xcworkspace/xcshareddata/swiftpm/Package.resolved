{"originHash": "236f5c9185822355ca5e86b3a2e2222ccd1e09a765c12628c8847a22722ee8ef", "pins": [{"identity": "autoinch", "kind": "remoteSourceControl", "location": "https://github.com/lixiang1994/AutoInch.git", "state": {"revision": "62fcfabe04f4d7e53d1045d80684a9ad8d3a2888", "version": "2.6.0"}}, {"identity": "purchases-ios-spm", "kind": "remoteSourceControl", "location": "https://github.com/RevenueCat/purchases-ios-spm.git", "state": {"revision": "eef133f4e8642675d924827f4b31a11f1ba5fc8a", "version": "5.24.0"}}, {"identity": "swift-case-paths", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-case-paths", "state": {"revision": "41b89b8b68d8c56c622dbb7132258f1a3e638b25", "version": "1.7.0"}}, {"identity": "swift-math-parser", "kind": "remoteSourceControl", "location": "https://github.com/bradhowes/swift-math-parser", "state": {"revision": "5729cd0d70f0c1a2f9e1551fd0914e22a0c6fa7c", "version": "3.7.3"}}, {"identity": "swift-parsing", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-parsing", "state": {"revision": "3432cb81164dd3d69a75d0d63205be5fbae2c34b", "version": "0.14.1"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-syntax", "state": {"revision": "f99ae8aa18f0cf0d53481901f88a0991dc3bd4a2", "version": "601.0.1"}}, {"identity": "xctest-dynamic-overlay", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/xctest-dynamic-overlay", "state": {"revision": "39de59b2d47f7ef3ca88a039dff3084688fe27f4", "version": "1.5.2"}}], "version": 3}