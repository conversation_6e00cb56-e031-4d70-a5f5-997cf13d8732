// CalculatorViewModel.swift
// 計算器視圖模型，連接UI和計算引擎

import Foundation
import SwiftUI

// MARK: - 通知名稱
extension Notification.Name {
    static let insertTextAtCursor = Notification.Name("insertTextAtCursor")
    static let deleteAtCursor = Notification.Name("deleteAtCursor")
    static let updateDisplayValue = Notification.Name("updateDisplayValue")
}

class CalculatorViewModel: ObservableObject {
    // MARK: - 發布屬性
    
    // 🔄 簡化：只需要一個 inputExpression
    @Published var inputExpression: String = ""
    
    // 🔄 保留但簡化：formattedInputExpression 現在只是 inputExpression 的別名
    @Published var formattedInputExpression: String = ""
    
    // 🔄 簡化：displayValue 改為即時計算結果顯示
    @Published var displayValue: String = "0"
    
    // 🔄 簡化：移除游標位置管理，讓 TextField 自己處理
    @Published var cursorPosition: Int = 0
    
    @Published var lastOperationWasEqual: Bool = false
    @Published var historyEntries: [HistoryEntry] = []
    @Published var currentPage: Int = 0
    @Published var expressionHasError: Bool = false
    
    // 最小顯示閾值 - 小於此值將切換為科學計數法
    private let minDisplayThreshold: Double = 0.0001
    // 最大顯示閾值 - 大於此值將切換為科學計數法
    private let maxDisplayThreshold: Double = 999999999
    
    // MARK: - 服務
    private let calculatorEngine = CalculatorEngine()
    private let historyRepository = HistoryRepository.shared
    private let numberFormatService = NumberFormatService.shared
    
    // MARK: - 初始化
    init() {
        clear()
        loadHistory()
        
        // 監聽歷史數據變化通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleHistoryDataChanged),
            name: .historyDataChanged,
            object: nil
        )
        
        // 監聽數字格式變化通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleNumberFormatChanged),
            name: .numberFormatChanged,
            object: nil
        )
        
        // 監聽顯示值更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleUpdateDisplayValue),
            name: .updateDisplayValue,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 通知處理
    
    /// 處理歷史數據變化通知
    @objc private func handleHistoryDataChanged() {
        DispatchQueue.main.async { [weak self] in
            self?.loadHistory()
        }
    }
    
    /// 處理數字格式變化通知
    @objc private func handleNumberFormatChanged() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 重新格式化 displayValue
            if !self.displayValue.isEmpty && self.displayValue != "0" {
                let cleanDisplayValue = self.numberFormatService.parseFormattedNumber(self.displayValue)
                if let number = Double(cleanDisplayValue) {
                    // 🔧 修復：使用 cleanNumberString 而不是格式化
                    self.displayValue = self.cleanNumberString(number)
                }
            }
            
            // 如果當前有完整表達式，重新計算顯示值
            self.updateDisplayValueRealtime()
        }
    }
    
    /// 處理顯示值更新通知
    @objc private func handleUpdateDisplayValue() {
        DispatchQueue.main.async { [weak self] in
            self?.updateDisplayValueRealtime()
        }
    }
    
    // MARK: - 公開方法
    
    /// 處理按鈕點擊
    /// - Parameter buttonType: 按鈕類型
    func buttonTapped(buttonType: ButtonType) {
        // 如果表達式有錯誤且不是清除按鈕，則忽略輸入
        if expressionHasError {
            if case .clear = buttonType {
                // 繼續執行
            } else {
                return
            }
        }
        
        // 🔄 使用通知系統在游標位置插入文字
        switch buttonType {
        case .digit(let number):
            NotificationCenter.default.post(name: .insertTextAtCursor, object: String(number))
        case .decimal:
            // 檢查當前數字是否已包含小數點
            if canInsertDecimalPoint() {
                let decimalSeparator = numberFormatService.decimalSeparator
                NotificationCenter.default.post(name: .insertTextAtCursor, object: decimalSeparator)
            }
        case .operation(let operation):
            let operatorSymbol: String
            switch operation {
            case .add: operatorSymbol = "+"
            case .subtract: operatorSymbol = "-"
            case .multiply: operatorSymbol = "×"
            case .divide: operatorSymbol = "÷"
            }
            
            // 如果表達式為空但有顯示值，使用顯示值
            if inputExpression.isEmpty && displayValue != "0" {
                inputExpression = displayValue
            }
            
            NotificationCenter.default.post(name: .insertTextAtCursor, object: operatorSymbol)
        case .equal:
            handleEqual()
        case .clear:
            clear()
        case .backspace:
            NotificationCenter.default.post(name: .deleteAtCursor, object: nil)
        case .parenthesis(let type):
            switch type {
            case .left: NotificationCenter.default.post(name: .insertTextAtCursor, object: "(")
            case .right: NotificationCenter.default.post(name: .insertTextAtCursor, object: ")")
            }
        }
        
        // 🔄 簡化：同步 formattedInputExpression
        formattedInputExpression = inputExpression
        
        // 注意：updateDisplayValueRealtime() 現在通過通知系統自動觸發
        // 只有在不涉及 inputExpression 更新的操作中才需要手動調用
        if case .equal = buttonType {
            // equal 按鈕不通過通知系統，需要手動觸發
        } else if case .clear = buttonType {
            // clear 按鈕不通過通知系統，需要手動觸發
            updateDisplayValueRealtime()
        }
    }
    
    /// 清除所有輸入和結果
    func clear() {
        inputExpression = ""
        formattedInputExpression = ""
        displayValue = "0"
        lastOperationWasEqual = false
        cursorPosition = 0
        expressionHasError = false
    }
    
    /// 加載歷史記錄
    func loadHistory() {
        historyEntries = historyRepository.getAllHistory()
    }
    
    /// 獲取隱藏的歷史記錄數量
    func getHiddenHistoryCount() -> Int {
        return historyRepository.getHiddenHistoryCount()
    }
    
    /// 檢查是否有 Pro 版本權限
    func hasProEntitlement() -> Bool {
        return historyRepository.getCurrentHistoryLimit() == nil
    }
    
    /// 清除所有歷史記錄
    func clearHistory() {
        historyRepository.clearAllHistory()
        historyEntries = []
    }
    
    /// 使用歷史記錄項目
    /// - Parameter entry: 歷史記錄項目
    func useHistoryEntry(_ entry: HistoryEntry) {
        inputExpression = entry.expression
        formattedInputExpression = entry.expression
        // 🔧 修復：使用 cleanNumberString 而不是格式化
        displayValue = cleanNumberString(Double(entry.result) ?? 0)
        lastOperationWasEqual = true
        expressionHasError = false
    }
    
    // MARK: - 私有輔助方法
    
    /// 處理數字按鈕
    private func handleDigit(_ digit: Int) {
        if lastOperationWasEqual {
            // 如果上一次操作是等號，則重新開始
            clear()
        }
        
        // 更新輸入表達式
        if inputExpression.isEmpty || endsWithOperator() || endsWithLeftParenthesis() {
            inputExpression += String(digit)
        } else {
            // 如果最後一次操作是右括號，添加乘號
            if endsWithRightParenthesis() {
                inputExpression += "*"
            } else {
                // 追加數字到表達式
                inputExpression += String(digit)
            }
        }
        
        // 更新顯示值為當前輸入的數字，使用千位分隔符
        updateDisplayValue(inputExpression)
    }
    
    /// 處理小數點
    private func handleDecimal() {
        if lastOperationWasEqual {
            // 如果上一次操作是等號，則重新開始
            clear()
            inputExpression = "0."
            updateDisplayValue("0.")
            return
        }
        
        // 檢查當前數字是否已包含小數點（需要先清理格式）
        if inputExpression.contains(".") {
            return
        }
        
        // 更新輸入表達式
        // 如果輸入表達式為空或以運算符結尾，先添加0
        if inputExpression.isEmpty || endsWithOperator() || endsWithLeftParenthesis() {
            inputExpression += "0."
        } else if endsWithRightParenthesis() {
            // 如果以右括號結尾，添加乘號和0
            inputExpression += "*0."
        } else {
            // 否則直接添加小數點
            inputExpression += "."
        }
        
        // 更新顯示值，使用千位分隔符
        updateDisplayValue(inputExpression)
    }
    
    /// 處理等號按鈕
    private func handleEqual() {
        guard !inputExpression.isEmpty else { return }
        
        do {
            // 將顯示符號和系統格式轉換為計算符號
            let calculationExpression = inputExpression
                .replacingOccurrences(of: "×", with: "*")
                .replacingOccurrences(of: "÷", with: "/")
                .replacingOccurrences(of: numberFormatService.decimalSeparator, with: ".")
            
            let result = try calculatorEngine.calculate(calculationExpression)
            
            // 🔧 修復：移除格式化，保持純數字
            let resultString = cleanNumberString(result)
            
            // 創建並保存歷史記錄
            let historyEntry = HistoryEntry(
                expression: inputExpression,
                result: resultString
            )
            historyRepository.addEntry(historyEntry)
            loadHistory()
            
            // 更新顯示 - 純數字，無格式化
            displayValue = resultString
            inputExpression = resultString // 將結果設為新的輸入
            
            lastOperationWasEqual = true
            expressionHasError = false
        } catch {
            displayValue = "Error"
            expressionHasError = true
            lastOperationWasEqual = true
        }
    }
    
    /// 計算表達式並更新顯示（抽取為單獨方法以便重用）
    private func calculateAndUpdateDisplay(expressionToCalculate: String) {
        do {
            // 檢查是否存在空括號對 ()
            if expressionToCalculate.contains("()") {
                // 不顯示任何錯誤訊息，直接返回，保持原狀
                return
            }
            
            // 調試輸出
            print("計算表達式: \(expressionToCalculate)")
            
            // 增強浮點數精度的計算
            let result = try calculatorEngine.calculate(expressionToCalculate)
            
            // 調試輸出
            print("原始計算結果: \(result)")
            
            let formattedResult = calculatorEngine.formatResult(result)
            
            // 調試輸出
            print("格式化後結果: \(formattedResult)")
            
            // 創建並保存歷史記錄 - 使用顯示給用戶的最終表達式
            let historyEntry = HistoryEntry(
                expression: expressionToCalculate, // 使用修正後的表達式
                result: formattedResult
            )
            historyRepository.addEntry(historyEntry)
            
            // 更新本地歷史記錄列表
            loadHistory()
            
            // 更新顯示和當前數字，使用千位分隔符
            updateDisplayValue(formattedResult)
            
            // 標記為等號操作
            lastOperationWasEqual = true
            expressionHasError = false
        } catch {
            // 處理並顯示錯誤
            if let calcError = error as? CalculatorEngine.CalculationError {
                updateDisplayValue(calcError.message)
            } else {
                updateDisplayValue("Error")
            }
            
            // 標記為錯誤狀態
            expressionHasError = true
            // 標記為等號操作，允許用戶清除錯誤
            lastOperationWasEqual = true
        }
    }
    
    /// 處理退格按鈕
    private func handleBackspace() {
        // 如果表達式有錯誤，則忽略退格操作
        if expressionHasError {
            return
        }
        
        // 如果上一次操作是等號，不要清除所有，保持原始表達式
        if lastOperationWasEqual {
            // 設置 lastOperationWasEqual 為 false，表示我們不再處於計算結果狀態
            lastOperationWasEqual = false
        }
        
        // 如果輸入表達式不為空，則刪除最後一個字符
        if !inputExpression.isEmpty {
            // 處理運算符（可能有空格）
            if endsWithOperator() {
                removeLastOperator()
                
                // 如果刪除運算符後，尋找前一個數字
                let components = inputExpression.components(separatedBy: ["+", "-", "*", "/", "(", ")"])
                let rawCurrentNumber = components.last ?? ""
                
                // 使用 updateDisplayValue 來正確格式化顯示
                if rawCurrentNumber.isEmpty {
                    displayValue = "0"
                } else {
                    updateDisplayValue(rawCurrentNumber)
                }
            } else {
                // 移除表達式的最後一個字符
                inputExpression.removeLast()
            }
        }
        
        // 如果輸入表達式為空，重置顯示和當前數字
        if inputExpression.isEmpty {
            displayValue = "0"
        }
    }
    
    /// 處理括號
    private func handleParenthesis(_ type: ParenthesisType) {
        if lastOperationWasEqual {
            clear()
        }
        
        switch type {
        case .left:
            // 處理左括號
            if !inputExpression.isEmpty && !endsWithOperator() && !endsWithLeftParenthesis() {
                // 如果表達式不為空且不以運算符或左括號結尾，添加乘號
                inputExpression += "*("
            } else {
                // 否則直接添加左括號
                inputExpression += "("
            }
            
        case .right:
            // 處理右括號（只有在有對應的左括號時才添加）
            if !inputExpression.isEmpty && !endsWithOperator() && countLeftParentheses() > countRightParentheses() {
                inputExpression += ")"
            }
        }
    }
    
    // MARK: - 輔助方法
    
    /// 檢查表達式是否以運算符結尾
    private func endsWithOperator() -> Bool {
        let trimmed = inputExpression.trimmingCharacters(in: .whitespaces)
        return trimmed.hasSuffix("+") ||
               trimmed.hasSuffix("-") ||
               trimmed.hasSuffix("×") ||
               trimmed.hasSuffix("÷")
    }
    
    /// 檢查表達式是否以左括號結尾
    private func endsWithLeftParenthesis() -> Bool {
        return inputExpression.hasSuffix("(")
    }
    
    /// 檢查表達式是否以右括號結尾
    private func endsWithRightParenthesis() -> Bool {
        return inputExpression.hasSuffix(")")
    }
    
    /// 移除表達式中的最後一個運算符
    private func removeLastOperator() {
        if endsWithOperator() {
            inputExpression.removeLast(1) // 移除最後一個運算符字符
        }
    }
    
    /// 計算表達式中的左括號數量
    private func countLeftParentheses() -> Int {
        return inputExpression.filter { $0 == "(" }.count
    }
    
    /// 計算表達式中的右括號數量
    private func countRightParentheses() -> Int {
        return inputExpression.filter { $0 == ")" }.count
    }
    
    /// 將數字字符串轉換為圖標字符串
    func convertToIconString(_ text: String) -> String {
        return text
    }
    
    /// 將顯示值轉換為表達式
    private func convertDisplayToExpression() -> String {
        return displayValue
    }
    
    /// 更新顯示值，應用於需要顯示數字的地方
    private func updateDisplayValue(_ number: String) {
        if let doubleValue = Double(number) {
            // 🔧 修復：使用 cleanNumberString 而不是格式化
            displayValue = cleanNumberString(doubleValue)
        } else {
            displayValue = number
        }
    }
    
    // MARK: - 🔄 簡化的格式化方法
    
    /// 格式化歷史記錄中的表達式（應用格式化mask）
    func formatHistoryExpression(_ expression: String) -> String {
        // 使用與 TextField 相同的格式化邏輯
        return formatExpressionForDisplay(expression)
    }
    
    /// 格式化歷史記錄中的結果（應用格式化mask）
    func formatHistoryResult(_ result: String) -> String {
        if Double(result) != nil {
            // 🔧 使用 NumberFormatService 進行格式化顯示
            return numberFormatService.formatForDisplay(result)
        }
        return result
    }
    
    /// 格式化表達式用於顯示（與 NewDisplayAreaView 中的邏輯一致）
    private func formatExpressionForDisplay(_ expression: String) -> String {
        var result = ""
        var currentNumber = ""
        
        for char in expression {
            if char.isNumber || char == "." {
                // 累積數字字符（底層使用標準點）
                currentNumber.append(char)
            } else {
                // 遇到操作符，先格式化累積的數字
                if !currentNumber.isEmpty {
                    let formattedNumber = formatNumberForDisplay(currentNumber)
                    result.append(formattedNumber)
                    currentNumber = ""
                }
                // 添加操作符
                result.append(char)
            }
        }
        
        // 處理最後的數字
        if !currentNumber.isEmpty {
            let formattedNumber = formatNumberForDisplay(currentNumber)
            result.append(formattedNumber)
        }
        
        return result
    }
    
    /// 格式化單個數字用於顯示
    private func formatNumberForDisplay(_ numberString: String) -> String {
        // 如果包含小數點，需要分別處理整數和小數部分
        if numberString.contains(".") {
            let parts = numberString.split(separator: ".", omittingEmptySubsequences: false)
            if parts.count >= 1 {
                let integerPart = String(parts[0])
                let decimalPart = parts.count > 1 ? String(parts[1]) : ""
                
                // 格式化整數部分（添加千位分隔符）
                let formattedInteger = numberFormatService.formatForDisplay(integerPart)
                
                // 組合結果，使用系統的小數分隔符
                return "\(formattedInteger)\(numberFormatService.decimalSeparator)\(decimalPart)"
            }
        }
        
        // 純整數，直接使用系統格式化
        return numberFormatService.formatForDisplay(numberString)
    }
    
    /// 🔄 即時計算並更新顯示值
    private func updateDisplayValueRealtime() {
        // 如果輸入為空，顯示 0
        guard !inputExpression.isEmpty else {
            displayValue = "0"
            return
        }
        
        // 先嘗試使用自動補完計算（包括不完整的括號）
        let result = calculateWithAutoComplete(inputExpression)
        if result != "0" || inputExpression == "0" {
            displayValue = result
            return
        }
        
        // 如果自動補完計算失敗，嘗試部分計算
        let partialResult = calculatePartialExpression(inputExpression)
        displayValue = partialResult
    }
    
    /// 計算部分表達式，返回合理的顯示結果
    private func calculatePartialExpression(_ expression: String) -> String {
        let trimmed = expression.trimmingCharacters(in: .whitespaces)
        
        // 如果以運算符結尾，嘗試計算到運算符前的部分
        let endsWithOp = endsWithOperator()
        
        if endsWithOp {
            // 移除最後的運算符
            var partialExpression = trimmed
            
            // 處理各種運算符結尾的情況（現在沒有空格）
            if partialExpression.hasSuffix("+") {
                partialExpression = String(partialExpression.dropLast(1))
            } else if partialExpression.hasSuffix("-") {
                partialExpression = String(partialExpression.dropLast(1))
            } else if partialExpression.hasSuffix("×") {
                partialExpression = String(partialExpression.dropLast(1))
            } else if partialExpression.hasSuffix("÷") {
                partialExpression = String(partialExpression.dropLast(1))
            }
            
            // 嘗試計算部分表達式（包括自動補完括號）
            if !partialExpression.isEmpty {
                let result = calculateWithAutoComplete(partialExpression)
                if result != "0" {
                    return result
                }
            }
        }
        
        // 🔧 修復：如果表達式包含運算符但不以運算符結尾，嘗試計算整個表達式
        if trimmed.contains("+") || trimmed.contains("-") || trimmed.contains("×") || trimmed.contains("÷") || trimmed.contains("(") {
            let result = calculateWithAutoComplete(trimmed)
            if result != "0" {
                return result
            }
        }
        
        // 如果只是一個數字，直接返回
        if let number = Double(trimmed.replacingOccurrences(of: numberFormatService.decimalSeparator, with: ".")) {
            let cleanResult = cleanNumberString(number)
            return cleanResult
        }
        
        // 最後備用方案
        return "0"
    }
    
    /// 計算表達式，自動補完缺失的右括號
    private func calculateWithAutoComplete(_ expression: String) -> String {
        var completedExpression = expression
        
        // 移除尾部的運算符（如果有的話）
        while completedExpression.hasSuffix("+") || 
              completedExpression.hasSuffix("-") || 
              completedExpression.hasSuffix("×") || 
              completedExpression.hasSuffix("÷") {
            completedExpression = String(completedExpression.dropLast())
        }
        
        // 如果移除運算符後表達式為空，返回 0
        if completedExpression.isEmpty {
            return "0"
        }
        
        // 計算缺失的右括號數量
        let leftParenCount = completedExpression.filter { $0 == "(" }.count
        let rightParenCount = completedExpression.filter { $0 == ")" }.count
        let missingRightParens = leftParenCount - rightParenCount
        
        // 自動補完缺失的右括號
        if missingRightParens > 0 {
            completedExpression += String(repeating: ")", count: missingRightParens)
        }
        
        // 處理連續括號的隱式乘法，例如 (1+2)(7 → (1+2)*(7)
        completedExpression = addImplicitMultiplication(completedExpression)
        
        do {
            let calculationExpression = completedExpression
                .replacingOccurrences(of: "×", with: "*")
                .replacingOccurrences(of: "÷", with: "/")
                .replacingOccurrences(of: numberFormatService.decimalSeparator, with: ".")
            
            let result = try calculatorEngine.calculate(calculationExpression)
            let cleanResult = cleanNumberString(result)
            return cleanResult
        } catch {
            // 如果計算失敗，嘗試獲取最後一個數字
            let lastNumber = getLastNumberFromExpression(expression)
            return lastNumber
        }
    }
    
    /// 添加隱式乘法符號，處理連續括號的情況
    private func addImplicitMultiplication(_ expression: String) -> String {
        var result = ""
        var previousChar: Character?
        
        for char in expression {
            if let prev = previousChar {
                // 檢查是否需要添加隱式乘法
                if (prev == ")" && char == "(") ||  // )( → )*(
                   (prev.isNumber && char == "(") || // 2( → 2*(
                   (prev == ")" && char.isNumber) {  // )2 → )*2
                    result += "*"
                }
            }
            result.append(char)
            previousChar = char
        }
        
        return result
    }
    
    /// 從表達式中獲取最後一個數字
    private func getLastNumberFromExpression(_ expression: String) -> String {
        // 備用方案：分割字符串獲取最後一個數字
        let components = expression.components(separatedBy: ["+", "-", "×", "÷", "(", ")"])
        for component in components.reversed() {
            let cleanComponent = component.replacingOccurrences(of: numberFormatService.thousandsSeparator, with: "")
            if let number = Double(cleanComponent.replacingOccurrences(of: numberFormatService.decimalSeparator, with: ".")) {
                return cleanNumberString(number)
            }
        }
        
        return "0"
    }
    
    /// 檢查表達式是否完整（可以計算）
    private func isExpressionComplete(_ expression: String) -> Bool {
        let trimmed = expression.trimmingCharacters(in: .whitespaces)
        
        // 空表達式不完整
        if trimmed.isEmpty {
            return false
        }
        
        // 檢查是否以運算符結尾（包括單獨的運算符）
        if trimmed.hasSuffix("+") || 
           trimmed.hasSuffix("-") || 
           trimmed.hasSuffix("×") || 
           trimmed.hasSuffix("÷") ||
           trimmed.hasSuffix("(") {
            return false
        }
        
        // 檢查括號是否匹配
        let leftParenCount = trimmed.filter { $0 == "(" }.count
        let rightParenCount = trimmed.filter { $0 == ")" }.count
        if leftParenCount != rightParenCount {
            return false
        }
        
        // 檢查是否包含至少一個數字
        let hasNumber = trimmed.rangeOfCharacter(from: CharacterSet.decimalDigits) != nil
        if !hasNumber {
            return false
        }
        
        // 檢查是否包含連續的運算符
        let operators = ["++", "+-", "+×", "+÷", "-+", "--", "-×", "-÷", 
                        "×+", "×-", "××", "×÷", "÷+", "÷-", "÷×", "÷÷"]
        for op in operators {
            if trimmed.contains(op) {
                return false
            }
        }
        
        return true
    }
    
    /// 檢查是否可以插入小數點（修復版，處理格式化數據）
    private func canInsertDecimalPoint() -> Bool {
        // 如果表達式為空，可以插入
        if inputExpression.isEmpty {
            return true
        }
        
        // 🔄 修復：獲取當前數字的原始值（移除格式化）
        let currentNumber = getCurrentNumberRaw()
        
        // 檢查當前數字是否包含小數點
        return !currentNumber.contains(".")
    }
    
    /// 獲取當前數字的原始值（移除格式化）
    private func getCurrentNumberRaw() -> String {
        // 從表達式末尾向前找到最後一個數字
        let components = inputExpression.components(separatedBy: ["+", "-", "×", "÷", "(", ")"])
        let lastComponent = components.last ?? ""
        
        // 移除千位分隔符，獲取原始數字
        let rawNumber = lastComponent.replacingOccurrences(of: numberFormatService.thousandsSeparator, with: "")
        return rawNumber
    }
    
    /// 🔧 清理數字字符串，移除所有格式化，保持純數字（底層數據）
    private func cleanNumberString(_ number: Double) -> String {
        // 處理特殊情況
        if number.isInfinite || number.isNaN {
            return "Error"
        }
        
        // 檢查是否為整數
        if number == floor(number) {
            // 整數：直接轉換，不要小數點
            return String(format: "%.0f", number)
        } else {
            // 小數：移除尾部零，但保留必要的小數位
            let formatter = NumberFormatter()
            formatter.numberStyle = .decimal
            formatter.usesGroupingSeparator = false // 🔧 關鍵：不使用千位分隔符
            formatter.minimumFractionDigits = 0
            formatter.maximumFractionDigits = 10
            formatter.locale = Locale(identifier: "en_US") // 🔧 底層數據使用標準格式，確保小數點是 "."
            
            if let formatted = formatter.string(from: NSNumber(value: number)) {
                return formatted
            }
            
            // 備用方案：確保使用標準點
            return String(number)
        }
    }
}
