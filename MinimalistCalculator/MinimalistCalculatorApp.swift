//
//  MinimalistCalculatorApp.swift
//  MinimalistCalculator
//
//  Created by <PERSON><PERSON> on 2025/4/15.
//

import SwiftUI
import RevenueCat
import RevenueCatUI

// 隱藏 Home Indicator 的 ViewController
class HomeIndicatorHidingController: UIViewController {
    override var prefersHomeIndicatorAutoHidden: Bool {
        return true // 隱藏 Home Indicator
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        setNeedsStatusBarAppearanceUpdate() // 更新狀態欄外觀
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        setNeedsStatusBarAppearanceUpdate() // 更新狀態欄外觀
    }
    
    override var prefersStatusBarHidden: Bool {
        return true // 隱藏狀態欄
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        // 設置控制器的基本屬性
        view.backgroundColor = HexColor.uiColor("000000")
        
        // 在 iOS 15 及以上版本中不再使用 UIApplication.shared.windows
        // 使用 UIWindowScene 的方式設置背景色
        if #available(iOS 15.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                windowScene.windows.forEach { window in
                    window.backgroundColor = HexColor.uiColor("000000")
                }
            }
        } else {
            // 舊版本的 iOS 仍然使用 UIApplication.shared.windows
            UIApplication.shared.windows.forEach { window in
                window.backgroundColor = HexColor.uiColor("000000")
            }
        }
    }
}

// SwiftUI 與 UIKit 的橋接器
struct HomeIndicatorHidingView<Content: View>: UIViewControllerRepresentable {
    var content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    func makeUIViewController(context: Context) -> UIViewController {
        let hostingController = UIHostingController(rootView: content)
        hostingController.view.backgroundColor = HexColor.uiColor("F9F9F9") // 設置 HostingController 的背景色
        
        let viewController = HomeIndicatorHidingController()
        viewController.view.backgroundColor = HexColor.uiColor("F9F9F9") // 再次確認背景色
        
        // 將 SwiftUI 視圖內嵌到我們的自定義控制器中
        viewController.addChild(hostingController)
        viewController.view.addSubview(hostingController.view)
        hostingController.view.frame = viewController.view.bounds
        hostingController.view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        hostingController.didMove(toParent: viewController)
        
        return viewController
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        if let hostingController = uiViewController.children.first as? UIHostingController<Content> {
            hostingController.rootView = content
        }
    }
}

@main
struct MinimalistCalculatorApp: App {
    // MARK: - 初始化
    init() {
        // 首先初始化語言服務，自動設定系統語言
        _ = LanguageService.shared
        
        // 配置 RevenueCat
        configureRevenueCat()
        
        // 標記已完成 onboarding（跳過語言選擇）
        UserDefaults.standard.set(true, forKey: "HasCompletedOnboarding")
        
        // 嘗試加載自定義字體
        var success = FontLoader.loadCustomFonts()
        
        // 如果第一種方法失敗，嘗試第二種方法
        if !success {
            Logger.info("嘗試使用Data方法加載字體...")
            success = FontLoader.loadCustomFontsUsingData()
        }

        if !success {
            Logger.warning("警告：自定義字體加載失敗，使用系統字體作為備用")
            // 打印所有可用字體以進行調試
            FontLoader.listAllAvailableFonts()
        }
    }
    
    // 配置 RevenueCat
    private func configureRevenueCat() {
        // 初始化 IAPService 單例以配置 RevenueCat
        _ = IAPService.shared
    }
    
    // MARK: - 視圖
    var body: some Scene {
        WindowGroup {
            // 使用包裝器隱藏 Home Indicator
            HomeIndicatorHidingView {
                ZStack {
                    // 底層顏色覆蓋全螢幕
                    HexColor.color("F9F9F9").edgesIgnoringSafeArea(.all)
                    
                    // 直接顯示主視圖，跳過 onboarding
                    MainPagingView()
                }
            }
            .background(HexColor.color("F9F9F9"))
            // 監聽 app 生命週期事件
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                // 當 app 從背景回到前景時，刷新數字格式偵測
                NumberFormatService.shared.refreshNumberFormat()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                // 當 app 變為活躍狀態時，也刷新數字格式偵測
                NumberFormatService.shared.refreshNumberFormat()
            }
        }
    }
}
