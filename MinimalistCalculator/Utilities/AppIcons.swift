// AppIcons.swift
// Source: 根據PRD v1.6提供的映射
// 此結構體定義了自定義TTF字體中的Unicode字符映射到計算機圖標

struct AppIcons {
    // MARK: - Digits & Decimal
    static let decimal: String          = "\u{E900}" // .
    static let zero: String             = "\u{E901}" // 0
    static let one: String              = "\u{E902}" // 1
    static let two: String              = "\u{E903}" // 2
    static let three: String            = "\u{E904}" // 3
    static let four: String             = "\u{E905}" // 4
    static let five: String             = "\u{E906}" // 5
    static let six: String              = "\u{E907}" // 6
    static let seven: String            = "\u{E908}" // 7
    static let eight: String            = "\u{E909}" // 8
    static let nine: String             = "\u{E90A}" // 9

    // MARK: - Control & Operators
    static let backspace: String        = "\u{E90B}" // ⌫ (Backspace Icon)
    static let parenthesisLeft: String  = "\u{E90C}" // ( (Left Parenthesis Icon)
    static let parenthesisRight: String = "\u{E90D}" // ) (Right Parenthesis Icon)
    static let clear: String            = "\u{E90E}" // C (Clear Icon, likely AC)
    static let division: String         = "\u{E90F}" // / (Division Icon)
    static let multiply: String         = "\u{E910}" // * (Multiplication Icon, e.g., ×)
    static let minus: String            = "\u{E911}" // - (Minus Icon)
    static let plus: String             = "\u{E912}" // + (Plus Icon)
    static let equal: String            = "\u{E913}" // = (Equal Icon)

    static let comma: String            = "\u{E91B}" // = (Comma Icon)

    static let check: String            = "\u{E914}" // ✓ (Check Icon)
    static let procheck: String         = "\u{E91A}" // ✓ (Pro Check Icon)

    // MARK: - Other UI Icons (Placeholders - CONFIRM with TTF & User if needed)
    // These need actual codepoints from the provided TTF file if used.
    static let clearHistory: String     = "\u{E915}" // Example: Clear history icon?
    static let close: String            = "\u{E916}" // Example: History icon codepoint?
    static let facebook: String         = "\u{E917}" // Example: History icon codepoint?
    static let instagram: String        = "\u{E918}" // Example: History icon codepoint?
    static let scrollDown: String       = "\u{E919}" // Example: History icon codepoint?
    static let scrollUp: String         = "\u{E920}" // Example: History icon codepoint?
    // static let settings: String      = "\u{E916}" // Example: Settings icon codepoint?
    // static let checkmark: String     = "\u{E917}" // Example: Checkmark for theme selection?
    // static let lock: String          = "\u{E918}" // Example: Lock for dark theme?
} 