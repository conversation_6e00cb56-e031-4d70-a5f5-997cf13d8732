import SwiftUI

struct HexColor {
    /// 將十六進制顏色代碼轉換為Color
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 對應的SwiftUI Color
    static func color(_ hex: String) -> Color {
        let (r, g, b, a) = hexToRGBA(hex)
        return Color(red: Double(r) / 255.0, green: Double(g) / 255.0, blue: Double(b) / 255.0, opacity: Double(a) / 255.0)
    }
    
    /// 將十六進制顏色代碼轉換為RGBA元組
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 包含紅、綠、藍、透明度的元組 (r, g, b, a)，每個值範圍為0-255
    static func hexToRGBA(_ hex: String) -> (r: Int, g: Int, b: Int, a: Int) {
        var hexString = hex.trimmingCharacters(in: .whitespacesAndNewlines).uppercased()
        
        // 移除可能存在的#前綴
        if hexString.hasPrefix("#") {
            hexString.remove(at: hexString.startIndex)
        }
        
        // 默認透明度為255（完全不透明）
        var alpha = 255
        var rgb = 0
        
        // 檢查是否有透明度值
        if hexString.count == 8 {
            // 格式為 RRGGBBAA
            if let intValue = Int(hexString, radix: 16) {
                alpha = intValue & 0xFF
                rgb = intValue >> 8
            }
        } else if hexString.count == 6 {
            // 格式為 RRGGBB
            if let intValue = Int(hexString, radix: 16) {
                rgb = intValue
            }
        } else {
            // 無效的十六進制格式
            print("無效的十六進制顏色格式: \(hex)")
            return (0, 0, 0, 255)
        }
        
        // 分離RGB值
        let red = (rgb >> 16) & 0xFF
        let green = (rgb >> 8) & 0xFF
        let blue = rgb & 0xFF
        
        return (red, green, blue, alpha)
    }
    
    /// 將十六進制顏色代碼轉換為UIColor
    /// - Parameter hex: 十六進制顏色代碼，格式可為: "F9F9F9" 或 "#F9F9F9"
    /// - Returns: 對應的UIColor
    static func uiColor(_ hex: String) -> UIColor {
        let (r, g, b, a) = hexToRGBA(hex)
        return UIColor(red: CGFloat(r) / 255.0, green: CGFloat(g) / 255.0, blue: CGFloat(b) / 255.0, alpha: CGFloat(a) / 255.0)
    }
}
