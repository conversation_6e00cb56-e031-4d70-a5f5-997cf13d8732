// AppIconsSymbol.swift
// 使用SF Symbols作為備用圖標

import SwiftUI

struct AppIconsSymbol {
    // 使用系統SF Symbols作為備用
    static func getSymbol(for char: String) -> Image {
        switch char {
        case "0", AppIcons.zero: return Image(systemName: "0.circle")
        case "1", AppIcons.one: return Image(systemName: "1.circle")
        case "2", AppIcons.two: return Image(systemName: "2.circle")
        case "3", AppIcons.three: return Image(systemName: "3.circle")
        case "4", AppIcons.four: return Image(systemName: "4.circle")
        case "5", AppIcons.five: return Image(systemName: "5.circle")
        case "6", AppIcons.six: return Image(systemName: "6.circle")
        case "7", AppIcons.seven: return Image(systemName: "7.circle")
        case "8", AppIcons.eight: return Image(systemName: "8.circle")
        case "9", AppIcons.nine: return Image(systemName: "9.circle")
        case ".", AppIcons.decimal: return Image(systemName: "circle")
        case "+", AppIcons.plus: return Image(systemName: "plus.circle")
        case "-", AppIcons.minus: return Image(systemName: "minus.circle")
        case "*", AppIcons.multiply: return Image(systemName: "multiply.circle")
        case "/", AppIcons.division: return Image(systemName: "divide.circle")
        case "=", AppIcons.equal: return Image(systemName: "equal.circle")
        case "⌫", AppIcons.backspace: return Image(systemName: "delete.left")
        case "C", AppIcons.clear: return Image(systemName: "clear")
        case "(", AppIcons.parenthesisLeft: return Image(systemName: "parentheses")
        case ")", AppIcons.parenthesisRight: return Image(systemName: "parentheses")
        default: return Image(systemName: "questionmark.circle")
        }
    }
    
    // 創建混合視圖，嘗試使用自定義字體，如果失敗則使用SF Symbols
    static func createView(for char: String, fontSize: CGFloat = 24, color: Color = .primary) -> some View {
        Group {
            if UIFont.fontNames(forFamilyName: FontLoader.customFontName).count > 0 {
                // 使用自定義字體
                Text(char)
                    .font(.custom(FontLoader.customFontName, size: fontSize))
                    .foregroundColor(color)
                    .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
                    .scaledToFit() // 確保圖標適應容器大小
            } else {
                // 使用SF Symbols作為備用
                getSymbol(for: char)
                    .font(.system(size: fontSize))
                    .foregroundColor(color)
                    .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
                    .imageScale(.medium) // 固定圖標大小
            }
        }
    }
    
    // 獲取圖標字符串的顯示視圖
    static func createDisplayView(for text: String, fontSize: CGFloat = 24, color: Color = .primary) -> some View {
        HStack(spacing: 0) {
            ForEach(0..<text.count, id: \.self) { index in
                let charIndex = text.index(text.startIndex, offsetBy: index)
                let char = String(text[charIndex])
                createView(for: char, fontSize: fontSize, color: color)
            }
        }
        .environment(\.sizeCategory, .medium) // 固定字體大小，不受系統設置影響
    }
} 