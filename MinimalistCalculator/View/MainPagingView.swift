// MainPagingView.swift
// 垂直分頁滾動視圖，包含計算器視圖和設定頁面

import SwiftUI
import UIKit
import AutoInch

struct MainPagingView: View {
    // MARK: - 屬性
    @StateObject private var viewModel = CalculatorViewModel()
    var horizontalPadding: CGFloat = CGFloat(60).auto() // 可調整的左右邊距，使用 AutoInch 進行等比例縮放
    
    // MARK: - 視圖
    var body: some View {
        VerticalPageView(viewModel: viewModel, horizontalPadding: horizontalPadding)
            .background(HexColor.color("F9F9F9"))
            .edgesIgnoringSafeArea(.all) // 使用 edgesIgnoringSafeArea 替代 ignoresSafeArea
    }
}

// 垂直分頁視圖包裝器
struct VerticalPageView: UIViewControllerRepresentable {
    @ObservedObject var viewModel: CalculatorViewModel
    var horizontalPadding: CGFloat
    @State private var currentPage = 0
    
    func makeUIViewController(context: Context) -> UIPageViewController {
        let pageViewController = UIPageViewController(
            transitionStyle: .scroll,
            navigationOrientation: .vertical) // 垂直方向導航
        
        pageViewController.dataSource = context.coordinator
        pageViewController.delegate = context.coordinator
        
        // 保存 pageViewController 到 coordinator
        context.coordinator.currentPageViewController = pageViewController
        
        // 設置初始頁面
        if let firstVC = context.coordinator.viewControllers.first {
            pageViewController.setViewControllers([firstVC], direction: .forward, animated: false)
        }
        
        return pageViewController
    }
    
    func updateUIViewController(_ pageViewController: UIPageViewController, context: Context) {
        // 更新邏輯 (如果需要)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIPageViewControllerDataSource, UIPageViewControllerDelegate {
        var parent: VerticalPageView
        var viewControllers: [UIViewController] = []
        weak var currentPageViewController: UIPageViewController?
        
        init(_ parent: VerticalPageView) {
            self.parent = parent
            super.init()
            
            // 初始化視圖控制器
            let calculatorVC = UIHostingController(
                rootView: CalculatorPageView(
                    viewModel: parent.viewModel, 
                    horizontalPadding: parent.horizontalPadding,
                    onNextPage: { [weak self] in
                        self?.navigateToNextPage()
                    }
                )
            )
            
            let settingsVC = UIHostingController(
                rootView: SettingsPageView(
                    viewModel: parent.viewModel,
                    scrollToTopAction: { [weak self] in
                        self?.navigateToPreviousPage()
                    }
                )
            )
            
            self.viewControllers = [calculatorVC, settingsVC]
        }
        
        // 編程方式導航到下一頁
        func navigateToNextPage() {
            guard let currentVC = currentPageViewController?.viewControllers?.first,
                  let currentIndex = viewControllers.firstIndex(of: currentVC),
                  currentIndex + 1 < viewControllers.count else {
                return
            }
            
            let nextVC = viewControllers[currentIndex + 1]
            currentPageViewController?.setViewControllers([nextVC], direction: .forward, animated: true)
        }
        
        // 編程方式導航到上一頁
        func navigateToPreviousPage() {
            guard let currentVC = currentPageViewController?.viewControllers?.first,
                  let currentIndex = viewControllers.firstIndex(of: currentVC),
                  currentIndex > 0 else {
                return
            }
            
            let prevVC = viewControllers[currentIndex - 1]
            currentPageViewController?.setViewControllers([prevVC], direction: .reverse, animated: true)
        }
        
        // UIPageViewControllerDataSource
        func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
            guard let index = viewControllers.firstIndex(of: viewController) else { return nil }
            let previousIndex = index - 1
            return previousIndex >= 0 ? viewControllers[previousIndex] : nil
        }
        
        func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
            guard let index = viewControllers.firstIndex(of: viewController) else { return nil }
            let nextIndex = index + 1
            return nextIndex < viewControllers.count ? viewControllers[nextIndex] : nil
        }
    }
}

// 計算器頁面視圖
struct CalculatorPageView: View {
    @ObservedObject var viewModel: CalculatorViewModel
    var horizontalPadding: CGFloat
    var onNextPage: () -> Void
    
    var body: some View {
        GeometryReader { geometry in
            CalculatorView(viewModel: viewModel)
                .overlay(alignment: .bottom) {
                    HStack {
                        Button {
                            onNextPage()
                        } label: {
                            AppIconsSymbol.createView(for: AppIcons.scrollUp, fontSize: CGFloat(44).auto(), color: HexColor.color("888888"))
                        }
                        Spacer() // 將按鈕推到左邊
                    }
                    // .padding(.bottom, CGFloat(30).auto()) // 調整底部邊距
                    .padding(.horizontal, horizontalPadding)
                }
        }
    }
}

// MARK: - 預覽
#Preview {
    MainPagingView()
} 