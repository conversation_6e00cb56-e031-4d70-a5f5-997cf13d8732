// HistoryEntryRowView.swift
// 歷史記錄項目行視圖

import SwiftUI
import AutoInch

struct HistoryEntryRowView: View {
    // MARK: - 屬性
    let entry: HistoryEntry
    let onTap: () -> Void
    @ObservedObject var viewModel: CalculatorViewModel
    @ObservedObject private var languageService = LanguageService.shared
    
    // MARK: - 視圖
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .trailing, spacing: CGFloat(2).auto()) {
                // 表達式 - 使用千位分隔符格式化
                Text(viewModel.formatHistoryExpression(entry.expression))
                    .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded)) // 使用系統字體
                    .foregroundColor(HexColor.color("6E6E6E"))
                    .lineLimit(1)
                
                // 結果 - 使用千位分隔符格式化
                Text(viewModel.formatHistoryResult(entry.result))
                    .font(.system(size: CGFloat(32).auto(), weight: .semibold, design: .rounded)) // 使用系統字體
                    .foregroundColor(HexColor.color("222222"))
                    .lineLimit(1)
                    .padding(.top, CGFloat(5).auto()) // 添加上方邊距

                // 日期（使用普通字體）- 現在會根據語言變更自動更新
                Text(entry.formattedDate)
                    .font(.system(size: CGFloat(12).auto(), weight: .regular, design: .rounded)) // 使用系統字體
                    // .font(.caption)
                    .foregroundColor(HexColor.color("6E6E6E"))
                    .padding(.top, CGFloat(5).auto()) // 添加上方邊距
                    .padding(.bottom, CGFloat(5).auto()) // 添加上方邊距
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Divider()
                    .background(HexColor.color("DEDEDE")) // 設置 Divider 顏色為黑色
                    // .padding(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .trailing)
            .padding(.top, CGFloat(50).auto())
            .padding(.horizontal, CGFloat(40).auto())
            // .background(Color(.systemGray6))
            // .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .onReceive(languageService.$currentLanguage) { _ in
            // 當語言變更時，SwiftUI 會自動重新渲染 entry.formattedDate
            // 這裡不需要額外的邏輯，只是確保視圖會響應語言變更
        }
    }
    
    // MARK: - 輔助方法
    
    /// 將普通字符串轉換為使用自定義圖標字體的字符串
    private func convertToIconString(_ text: String) -> String {
        var result = ""
        
        for char in text {
            switch char {
            case "0": result += AppIcons.zero
            case "1": result += AppIcons.one
            case "2": result += AppIcons.two
            case "3": result += AppIcons.three
            case "4": result += AppIcons.four
            case "5": result += AppIcons.five
            case "6": result += AppIcons.six
            case "7": result += AppIcons.seven
            case "8": result += AppIcons.eight
            case "9": result += AppIcons.nine
            case ".": 
                // 根據系統的小數分隔符選擇圖標
                let numberFormatService = NumberFormatService.shared
                result += numberFormatService.decimalSeparator == "," ? AppIcons.comma : AppIcons.decimal
            case ",":
                // 根據系統的小數分隔符選擇圖標  
                let numberFormatService = NumberFormatService.shared
                result += numberFormatService.decimalSeparator == "," ? AppIcons.comma : AppIcons.decimal
            case "+": result += AppIcons.plus
            case "-": result += AppIcons.minus
            case "*": result += AppIcons.multiply
            case "/": result += AppIcons.division
            case "(": result += AppIcons.parenthesisLeft
            case ")": result += AppIcons.parenthesisRight
            case "=": result += AppIcons.equal
            default: result += String(char)
            }
        }
        
        return result
    }
} 