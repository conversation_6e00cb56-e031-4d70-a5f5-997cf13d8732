// ProIntroductionView.swift
// PRO版功能介紹頁面

import SwiftUI
import RevenueCatUI
import RevenueCat
import AutoInch

struct ProIntroductionView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var iapService = IAPService.shared
    @State private var showingPaywall = false
    @State private var isYearly = false
    @State private var selectedPackage: Package?
    @State private var monthlyPackage: Package?
    @State private var yearlyPackage: Package?
    @State private var isRestoring = false
    @State private var isPurchasing = false

    @State private var isShowingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    
    var body: some View {
        ScrollView {
            HStack {
                // 關閉按鈕
                Button(action: {
                    dismiss()
                }) {
                    AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.color("222222")) // 使用 scrollDown 圖示
                }
                
                Spacer()
                
            }
            .padding(.top, CGFloat(40).auto()) // 這裡控制工具欄距離頂部的距離
            .padding(.horizontal, CGFloat(40).auto())
            
            VStack(spacing: CGFloat(0).auto()) {
                
                
                // 6. 購買按鈕
                if iapService.isEntitlementActive("pro") {
                    VStack(spacing: CGFloat(8).auto()) {
                        // 1. 主標題
                        Text("pro_title".localized)
                            .font(.system(size: CGFloat(36).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.color("222222"))
                            .padding(.top, CGFloat(12).auto())

                        AppIconsSymbol.createView(for: AppIcons.procheck, fontSize: CGFloat(100).auto(), color: HexColor.color("222222"))
                        .padding(.top, CGFloat(30).auto())
                        
                        // 2. 副標題
                        Text("pro_thanks_message".localized)
                            .multilineTextAlignment(.center) // 將文字對齊到中心
                            .font(.system(size: CGFloat(18).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.color("222222"))
                            .frame(width: CGFloat(228).auto(), alignment: .center)

                        // 3. Describe
                        Text("pro_description".localized)
                            .lineLimit(nil) // 允許多行顯示
                            .lineSpacing(6) // 設置行高
                            .multilineTextAlignment(.center) // 將文字對齊到中心
                            .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                            .foregroundColor(HexColor.color("888888"))
                            .frame(width: CGFloat(228).auto(), alignment: .center)
                            .padding(.top, CGFloat(16).auto())
                            .padding(.bottom, CGFloat(80).auto())

                        HStack(spacing: CGFloat(10).auto()) {
                            Text("→")
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .opacity(0)
                            Text("pro_feedback".localized)
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.color("FFFFFF"))
                                .onTapGesture {
                                    if let url = URL(string: "https://minlsm.featurebase.app/") {
                                        UIApplication.shared.open(url)
                                    }
                                }
                            Text("→")
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.color("FFFFFF"))
                        }
                        .padding(.vertical, CGFloat(10).auto())
                        .frame(width: CGFloat(200).auto())
                        .background(HexColor.color("222222"))
                        .cornerRadius(CGFloat(5).auto())
                    }  
                    
                } else {
                    VStack(spacing: CGFloat(8).auto()) {
                        // 1. 主標題
                        Text("pro_title".localized)
                            .font(.system(size: CGFloat(36).auto(), weight: .medium, design: .rounded))
                            .foregroundColor(HexColor.color("222222"))
                            .padding(.top, CGFloat(4).auto())
                        
                        // 2. 副標題
                        Text("pro_unlock_experience".localized)
                            .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                            .foregroundColor(HexColor.color("888888"))
                            // .padding(.bottom, CGFloat(30).auto())
                    }                    
                    // 3. 訂閱選項切換器
                    SubscriptionToggle(isYearly: $isYearly)
                        .padding(.horizontal, CGFloat(40).auto())
                        .padding(.top, CGFloat(30).auto())
                        .padding(.bottom, CGFloat(20).auto())
                    
                    // 4. 價格顯示
                    HStack {
                        if let package = isYearly ? yearlyPackage : monthlyPackage {
                            // 顯示訂閱周期
                            Text(isYearly ? "subscription_per_year".localized : "subscription_per_month".localized)
                                .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                                // .foregroundColor(HexColor.color("888888"))
                                .opacity(0)
                                .offset(y: CGFloat(4).auto())
                                // .padding(.top, CGFloat(4).auto())
                                
                            // 顯示價格
                            Text(package.storeProduct.localizedPriceString)
                                .font(.system(size: CGFloat(30).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.color("222222"))
                            
                            // 如果有折扣，顯示原價
                            // if isYearly, let monthly = monthlyPackage?.storeProduct.localizedPriceString {
                            //     Text("原價：\(monthly) x 12")
                            //         .font(.system(size: CGFloat(14).auto()))
                            //         .foregroundColor(.gray)
                            //         .strikethrough()
                            // }
                            
                            // 顯示訂閱周期
                            Text(isYearly ? "subscription_per_year".localized : "subscription_per_month".localized)
                                .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.color("888888"))
                                .offset(y: CGFloat(4).auto())
                                // .padding(.top, CGFloat(4).auto())
                        } else {
                            Text("pro_loading".localized)
                                .font(.system(size: CGFloat(30).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.color("888888"))
                        }
                    }
                    .padding(.bottom, CGFloat(40).auto())  
                    // 5. 功能列表
                    VStack(alignment: .leading, spacing: CGFloat(0).auto()) {
                        FeatureItem(color: HexColor.color("222222"), title: "pro_unlimited_history".localized)
                        
                        FeatureItem(color: HexColor.color("222222"), title: "pro_all_future_features".localized)
                    }
                    .padding(.horizontal, CGFloat(30).auto())
                    .padding(.bottom, CGFloat(40).auto())
                    // 如果未購買，顯示購買按鈕
                    Button {
                        if let package = isYearly ? yearlyPackage : monthlyPackage {
                            purchasePackage(package)
                        }
                    } label: {
                        if isPurchasing {
                            HStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: HexColor.color("FFFFFF")))
                                    .opacity(0)
                                    
                                Text("pro_purchasing".localized)
                                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.color("FFFFFF"))
                                
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: HexColor.color("FFFFFF")))
                            }
                            .padding(.vertical, CGFloat(10).auto())
                            .frame(width: CGFloat(200).auto())
                            .background(HexColor.color("222222"))
                            .cornerRadius(CGFloat(5).auto())
                        } else {
                            Text("pro_upgrade".localized)
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.color("FFFFFF"))
                                .padding(.vertical, CGFloat(10).auto())
                                .frame(width: CGFloat(200).auto())
                                .background(HexColor.color("222222"))
                                .cornerRadius(CGFloat(5).auto())
                        }
                    }
                    // 7. 恢復購買按鈕
                    Button {
                        restorePurchases()
                    } label: {
                        if isRestoring {
                            HStack {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: HexColor.color("222222")))
                                    .opacity(0)
                                    
                                Text("pro_restoring".localized)
                                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                    .foregroundColor(HexColor.color("222222"))
                                
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: HexColor.color("222222")))
                            }
                            .padding(.vertical, CGFloat(10).auto())
                            .frame(width: CGFloat(200).auto())
                            .background(HexColor.color("DEDEDE"))
                            .cornerRadius(CGFloat(5).auto())
                        } else {
                            Text("pro_restore".localized)
                                .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                                .foregroundColor(HexColor.color("222222"))
                                .padding(.vertical, CGFloat(10).auto())
                                .frame(width: CGFloat(200).auto())
                                .background(HexColor.color("DEDEDE"))
                                .cornerRadius(CGFloat(5).auto())
                        }
                    }
                    .padding(.top, CGFloat(10).auto())                    
                    .disabled(isRestoring)
                    .padding(.horizontal, CGFloat(40).auto())
                    .disabled(selectedPackage == nil || isPurchasing)

                    Text(buildAgreementText())
                        .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.color("888888"))
                        .multilineTextAlignment(.center)
                        .padding(.top, CGFloat(12).auto())                    
                        .padding(.bottom, CGFloat(40).auto())
                        .padding(.horizontal, CGFloat(40).auto())
                }
                
                
            }
        }
        .background(HexColor.color("F9F9F9"))
        // .navigationBarItems(trailing: Button("關閉") {
        //     dismiss()
        // })
        .onAppear {
            // 在視圖出現時，刷新顧客信息
            iapService.updateCustomerInfo()
            // 獲取商品
            loadOfferings()
        }
        .onChange(of: isYearly) { newValue in
            // 當切換訂閱類型時，更新選擇的套餐
            selectedPackage = newValue ? yearlyPackage : monthlyPackage
        }
        .alert(isPresented: $isShowingAlert) {
            Alert(title: Text(alertTitle), message: Text(alertMessage), dismissButton: .default(Text("ok".localized)))
        }

    }

    private func buildAgreementText() -> AttributedString {
        var str = AttributedString("pro_purchase_agreement".localized)

        // 根據當前語言匹配不同的文字
        let currentLanguage = LanguageService.shared.currentLanguage
        
        switch currentLanguage {
        case .english:
            if let termsRange = str.range(of: "Terms") {
                str[termsRange].foregroundColor = HexColor.color("222222")
                str[termsRange].underlineStyle = .single
                str[termsRange].link = URL(string: "https://www.minlsm.com/terms")
            }
            if let policyRange = str.range(of: "Privacy Policy") {
                str[policyRange].foregroundColor = HexColor.color("222222")
                str[policyRange].underlineStyle = .single
                str[policyRange].link = URL(string: "https://www.minlsm.com/privacy")
            }
        case .traditionalChinese:
            if let termsRange = str.range(of: "服務條款") {
                str[termsRange].foregroundColor = HexColor.color("222222")
                str[termsRange].underlineStyle = .single
                str[termsRange].link = URL(string: "https://www.minlsm.com/terms")
            }
            if let policyRange = str.range(of: "隱私政策") {
                str[policyRange].foregroundColor = HexColor.color("222222")
                str[policyRange].underlineStyle = .single
                str[policyRange].link = URL(string: "https://www.minlsm.com/privacy")
            }
        case .japanese:
            if let termsRange = str.range(of: "利用規約") {
                str[termsRange].foregroundColor = HexColor.color("222222")
                str[termsRange].underlineStyle = .single
                str[termsRange].link = URL(string: "https://www.minlsm.com/terms")
            }
            if let policyRange = str.range(of: "プライバシーポリシー") {
                str[policyRange].foregroundColor = HexColor.color("222222")
                str[policyRange].underlineStyle = .single
                str[policyRange].link = URL(string: "https://www.minlsm.com/privacy")
            }
        }

        return str
    }
    
    // 載入商品信息
    private func loadOfferings() {
        iapService.getOfferings { offerings in
            if let current = offerings?.current {
                DispatchQueue.main.async {
                    // 月度套餐
                    self.monthlyPackage = current.monthly
                    
                    // 年度套餐
                    self.yearlyPackage = current.annual
                    
                    // 根據當前選擇設置默認套餐
                    self.selectedPackage = self.isYearly ? self.yearlyPackage : self.monthlyPackage
                }
            }
        }
    }
    
    // 處理購買
    private func purchasePackage(_ package: Package) {
        // 設置正在購買狀態
        isPurchasing = true
        
        iapService.purchasePackage(package) { success, customerInfo, error in
            // 購買完成後，關閉加載狀態
            DispatchQueue.main.async {
                self.isPurchasing = false
                
                if success {
                    // 更新用戶狀態
                    self.iapService.updateCustomerInfo()
                    
                    // 刷新歷史記錄
                    HistoryRepository.shared.refreshProStatus()
                    
                    // 顯示成功訊息
                    self.alertTitle = "pro_subscription_success".localized
                    self.alertMessage = "pro_thank_you_message".localized
                    self.isShowingAlert = true
                    print("購買成功")
                } else if let error = error {
                    // 如果是用戶取消，不顯示錯誤
                    if (error as NSError).domain == "SKErrorDomain" && (error as NSError).code == 2 {
                        print("用戶取消購買")
                    } else {
                        // 顯示錯誤訊息
                        self.alertTitle = "pro_purchase_failed".localized
                        self.alertMessage = error.localizedDescription
                        self.isShowingAlert = true
                        print("購買失敗: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
    
    // 恢復購買
    private func restorePurchases() {
        isRestoring = true

        Purchases.shared.restorePurchases { customerInfo, error in
            DispatchQueue.main.async {
                self.isRestoring = false

                if let error = error {
                    self.alertTitle = "pro_restore_failed".localized
                    self.alertMessage = error.localizedDescription
                    self.isShowingAlert = true
                    print("恢復購買失敗: \(error.localizedDescription)")
                    return
                }

                guard let customerInfo = customerInfo else {
                    self.alertTitle = "pro_error".localized
                    self.alertMessage = "pro_cannot_get_user_info".localized
                    self.isShowingAlert = true
                    return
                }

                let isPro = customerInfo.entitlements["pro"]?.isActive == true

                if isPro {
                    self.alertTitle = "pro_restore_success".localized
                    self.alertMessage = "pro_purchase_restored".localized
                    print("恢復購買成功，entitlement 有效")

                    // 自訂回傳處理
                    self.iapService.updateCustomerInfo()
                    HistoryRepository.shared.refreshProStatus()

                } else {
                    self.alertTitle = "pro_no_restorable_items".localized
                    self.alertMessage = "pro_no_restorable_message".localized
                    print("恢復完成，但沒有 entitlement")
                }

                self.isShowingAlert = true
            }
        }
    }

    
    // 顯示警告框
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "ok".localized, style: .default, handler: nil))
        
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            rootViewController.present(alert, animated: true, completion: nil)
        }
    }
}

// 功能項目視圖
struct FeatureItem: View {
    // let icon: String
    let color: Color
    let title: String
    // let description: String
    
    var body: some View {
        HStack(spacing: CGFloat(4).auto()) {
            AppIconsSymbol.createView(for: AppIcons.check, fontSize: CGFloat(40).auto(), color: color)
                // .frame(width: CGFloat(36).auto(), height: CGFloat(36).auto())

            Text(title)
                    .font(.system(size: CGFloat(16).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.color("222222"))
            // VStack(alignment: .leading, spacing: CGFloat(4).auto()) {
            //     Text(title)
            //         .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                
            //     Text(description)
            //         .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
            //         .foregroundColor(HexColor.color("888888"))
            // }
        }
    }
}

// 月度/年度切換器
struct SubscriptionToggle: View {
    @Binding var isYearly: Bool
    
    var body: some View {
        HStack {
            Spacer()
            
            // 月度選項
            ToggleOption(
                title: "pro_monthly_plan".localized,
                isSelected: !isYearly,
                action: { 
                    withAnimation {
                        isYearly = false
                    }
                }
            )
            
            // 年度選項
            ToggleOption(
                title: "pro_yearly_plan".localized,
                isSelected: isYearly,
                discount: "pro_discount_50".localized,
                action: { 
                    withAnimation {
                        isYearly = true
                    }
                }
            )
            
            Spacer()
        }
        .padding(CGFloat(4).auto())
        // .background(Color.gray.opacity(0.1))
        .cornerRadius(CGFloat(5).auto())
    }
}

// 切換選項
struct ToggleOption: View {
    let title: String
    let isSelected: Bool
    var discount: String? = nil
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: CGFloat(2).auto()) {
                Text(title)
                    .font(.system(size: CGFloat(14).auto(), weight: isSelected ? .medium : .medium, design: .rounded))
                    .foregroundColor(isSelected ? HexColor.color("222222") : HexColor.color("888888"))
                
                if let discount = discount {
                    Text(discount)
                        .font(.system(size: CGFloat(14).auto(), weight: .medium, design: .rounded))
                        .foregroundColor(isSelected ? HexColor.color("009966") : HexColor.color("009966"))
                }
            }
            .frame(width: CGFloat(94).auto())
            .padding(.vertical, CGFloat(8).auto())
            .padding(.horizontal, CGFloat(10).auto())
            .background(isSelected ? HexColor.color("DEDEDE") : Color.clear)
            .cornerRadius(CGFloat(5).auto())
        }
    }
}

// MARK: - 預覽
#if DEBUG
struct ProIntroductionView_Previews: PreviewProvider {
    static var previews: some View {
        ProIntroductionView()
    }
}
#endif 
