// CalculatorButtonView.swift
// 自定義計算機按鈕視圖，使用AppIcons和自定義字體顯示計算器按鈕

import SwiftUI
import AutoInch

struct CalculatorButtonView: View {
    // MARK: - 屬性
    let iconString: String // 來自AppIcons的字符映射
    let buttonColor: Color // 按鈕背景顏色
    let textColor: Color // 按鈕文字顏色
    let fontSize: CGFloat // 文字大小
    let action: () -> Void // 按鈕點擊動作
    
    // MARK: - 初始化
    init(
        iconString: String,
        buttonColor: Color = Color(.systemGray5),
        textColor: Color = .black,
        fontSize: CGFloat = CGFloat(24).auto(),
        action: @escaping () -> Void
    ) {
        self.iconString = iconString
        self.buttonColor = buttonColor
        self.textColor = textColor
        self.fontSize = fontSize
        self.action = action
    }
    
    // MARK: - 視圖
    var body: some View {
        Button(action: action) {
            ZStack {
                Circle()
                    .fill(buttonColor)
                
                // 使用安全的圖標顯示方法，傳入自定義字體大小
                AppIconsSymbol.createView(for: iconString, fontSize: fontSize, color: textColor)
            }
        }
        .aspectRatio(1.0, contentMode: .fit)
    }
}

// MARK: - 預覽
#Preview {
    HStack {
        CalculatorButtonView(iconString: AppIcons.one, fontSize: 20, action: {})
        CalculatorButtonView(iconString: AppIcons.plus, buttonColor: .orange, fontSize: 30, action: {})
    }
    .padding()
} 
