// DisplayAreaView.swift
// 計算機顯示區域，使用 TextField 處理輸入表達式和顯示計算結果

import SwiftUI
import AutoInch

// MARK: - 無鍵盤 TextField
struct NoKeyboardTextField: UIViewRepresentable {
    @Binding var text: String
    let placeholder: String
    let font: UIFont
    let textColor: UIColor
    
    init(text: Binding<String>, placeholder: String = "", font: UIFont, textColor: UIColor) {
        self._text = text
        self.placeholder = placeholder
        self.font = font
        self.textColor = textColor
    }

    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        textField.inputView = UIView() // 隱藏鍵盤
        textField.inputAccessoryView = UIView() // 隱藏鍵盤工具列
        textField.placeholder = placeholder
        textField.textColor = textColor
        textField.font = font
        textField.textAlignment = .right
        textField.backgroundColor = .clear
        textField.borderStyle = .none
        textField.delegate = context.coordinator
        return textField
    }

    func updateUIView(_ textField: UITextField, context: Context) {
        textField.text = text
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(text: $text)
    }

    class Coordinator: NSObject, UITextFieldDelegate {
        @Binding var text: String
        
        init(text: Binding<String>) {
            self._text = text
        }
        
        // 可以在這裡處理文字變化，但由於我們禁用了鍵盤，主要是透過外部更新
        func textFieldDidChangeSelection(_ textField: UITextField) {
            // 保持同步，但主要是透過 updateUIView 來更新
        }
    }
}

struct DisplayAreaView: View {
    // MARK: - 屬性
    @Binding var inputExpression: String // 輸入表達式（雙向綁定）
    let displayValue: String // 顯示值/計算結果
    var horizontalPadding: CGFloat = CGFloat(0).auto() // 可調整的左右邊距
    
    // 數字格式服務
    private let numberFormatService = NumberFormatService.shared
    
    // MARK: - 計算屬性：格式化顯示
    
    /// 格式化後的輸入表達式（僅用於顯示）
    private var formattedInputExpression: String {
        return formatExpressionForDisplay(inputExpression)
    }
    
    /// 格式化後的顯示值（僅用於顯示）
    private var formattedDisplayValue: String {
        return numberFormatService.formatForDisplay(displayValue)
    }
    
    // MARK: - 視圖
    var body: some View {
        VStack(alignment: .trailing, spacing: CGFloat(0).auto()) {
            // 輸入表達式區域 - 使用原生 TextField，支援橫向滾動
            GeometryReader { geometry in
                ScrollViewReader { proxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            NoKeyboardTextField(
                                text: .constant(formattedInputExpression),
                                font: {
                                    if let descriptor = UIFontDescriptor.preferredFontDescriptor(withTextStyle: .body)
                                        .withDesign(.rounded) {
                                        return UIFont(descriptor: descriptor, size: CGFloat(18).auto())
                                    } else {
                                        return UIFont.systemFont(ofSize: CGFloat(18).auto(), weight: .regular)
                                    }
                                }(),
                                textColor: UIColor(HexColor.color("6E6E6E"))
                            )
                            .frame(height: CGFloat(30).auto())
                            .fixedSize(horizontal: true, vertical: false)
                            .id("textfield")
                        }
                        .frame(minWidth: geometry.size.width - horizontalPadding * 2, alignment: .trailing)
                    }
                    .frame(height: CGFloat(30).auto())
                    .padding(.trailing, CGFloat(4).auto()) // 添加右邊距
                    // .padding(.horizontal, horizontalPadding)
                    .onChange(of: inputExpression) { _ in
                        // 當文字改變時，滾動到右邊（顯示個位數）
                        withAnimation(.easeInOut(duration: 0.2)) {
                            proxy.scrollTo("textfield", anchor: .trailing)
                        }
                    }
                    .onAppear {
                        // 初始時滾動到右邊
                        proxy.scrollTo("textfield", anchor: .trailing)
                    }
                }
            }
            .frame(height: CGFloat(30).auto())
            
            // 計算結果區域 - 支援橫向滾動，不縮放字體
            GeometryReader { geometry in
                ScrollViewReader { proxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            Text(formattedDisplayValue)
                                .font(.system(size: CGFloat(77).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.color("222222"))
                                .lineLimit(1)
                                .frame(height: CGFloat(77).auto())
                                .fixedSize(horizontal: true, vertical: false)
                                .id("displaytext")
                        }
                        .frame(minWidth: geometry.size.width - horizontalPadding * 2, alignment: .trailing)
                    }
                    .frame(height: CGFloat(77).auto())
                    .padding(.horizontal, horizontalPadding)
                    .onChange(of: displayValue) { _ in
                        // 當顯示值改變時，滾動到右邊（顯示個位數）
                        withAnimation(.easeInOut(duration: 0.2)) {
                            proxy.scrollTo("displaytext", anchor: .trailing)
                        }
                    }
                    .onAppear {
                        // 初始時滾動到右邊
                        proxy.scrollTo("displaytext", anchor: .trailing)
                    }
                }
            }
            .frame(height: CGFloat(77).auto())
        }
        .background(HexColor.color("F9F9F9"))
    }
}

// MARK: - 格式化方法
extension DisplayAreaView {
    
    /// 格式化表達式用於顯示（添加千位分隔符）
    private func formatExpressionForDisplay(_ expression: String) -> String {
        // 分割表達式為 tokens（數字和操作符）
        var result = ""
        var currentNumber = ""
        
        for char in expression {
            if char.isNumber || char == "." {
                // 累積數字字符
                currentNumber.append(char)
            } else {
                // 遇到操作符，先處理累積的數字
                if !currentNumber.isEmpty {
                    let formattedNumber = numberFormatService.formatForDisplay(currentNumber)
                    result.append(formattedNumber)
                    currentNumber = ""
                }
                // 添加操作符
                result.append(char)
            }
        }
        
        // 處理最後的數字（如果有）
        if !currentNumber.isEmpty {
            let formattedNumber = numberFormatService.formatForDisplay(currentNumber)
            result.append(formattedNumber)
        }
        
        return result
    }
}

// MARK: - 預覽
#Preview {
    struct PreviewWrapper: View {
        @State private var testExpression = "1 + 2"
        
        var body: some View {
            DisplayAreaView(
                inputExpression: $testExpression,
                displayValue: "3"
            )
        }
    }
    
    return PreviewWrapper()
} 
