// SettingsPageView.swift
// 設定頁面視圖，包含歷史記錄、語言設定、深色模式和關於等選項

import SwiftUI
import AutoInch
import RevenueCat
import RevenueCatUI

struct SettingsPageView: View {
    // MARK: - 屬性
    @ObservedObject var viewModel: CalculatorViewModel
    @ObservedObject private var iapService = IAPService.shared
    @ObservedObject private var languageService = LanguageService.shared
    @ObservedObject private var numberFormatService = NumberFormatService.shared
    @ObservedObject private var hapticService = HapticService.shared
    @State private var showingHistory = false
    @State private var showingProIntro = false
    @State private var showingNumberFormat = false
    @State private var showingHapticSettings = false
    let scrollToTopAction: () -> Void
    @State private var displayAreaPadding: CGFloat = CGFloat(60).auto()
    
    // MARK: - 視圖
    var body: some View {
        VStack(spacing: 0) {
            
            
            // 設定列表
            VStack(alignment: .leading, spacing: CGFloat(20).auto()) {
                // 返回頂部按鈕
                HStack {
                    // Spacer()
                    Button {
                        scrollToTopAction()
                    } label: {
                        AppIconsSymbol.createView(for: AppIcons.scrollDown, fontSize: CGFloat(44).auto(), color: HexColor.color("888888")) // 使用 RGB 顏色
                    }
                }
                .padding(.top, CGFloat(50).auto())
                .frame(maxWidth: .infinity, alignment: .leading)
                // 歷史記錄選項
                SettingsRow(title: "settings_history".localized) {
                    showingHistory = true
                }
                .padding(.top, CGFloat(60).auto())
                
                // 觸覺反饋選項
                SettingsRow(title: "settings_haptic".localized) {
                    showingHapticSettings = true
                }
                
                // 數字格式選項
                // SettingsRowWithSubtitle(
                //     title: "數字格式",
                //     subtitle: numberFormatService.getFormatExample()
                // ) {
                //     showingNumberFormat = true
                // }
                
                // 深色模式選項
                // SettingsRow(title: "Dark mode") {
                //     // 這裡應該切換深色模式
                //     print("Dark Mode Tapped")
                // }
                // Divider().padding(.leading)
                
                //IAP Pro 選項
                SettingsRow(title: iapService.isEntitlementActive("pro") ? "settings_pro".localized : "settings_pro".localized) {
                    // 如果未訂閱，則打開PRO介紹頁面
                    // if !iapService.isEntitlementActive("pro") {
                        showingProIntro = true
                    // }
                }
                
                // 關於選項
                SettingsRow(title: "settings_feedback".localized) {
                    // 打開 Google 網站
                    if let url = URL(string: "https://minlsm.featurebase.app/") {
                        UIApplication.shared.open(url)
                    }
                }

                SettingsRow(title: "settings_about".localized) {
                    // 打開 Google 網站
                    if let url = URL(string: "https://www.minlsm.com/") {
                        UIApplication.shared.open(url)
                    }
                }
                
                // #if DEBUG
                // // 開發用：重置 onboarding（僅在 Debug 模式下顯示）
                // SettingsRow(title: "Reset Onboarding (Debug)") {
                //     UserDefaults.standard.set(false, forKey: "HasCompletedOnboarding")
                //     // 重啟 App 以看到效果
                //     exit(0)
                // }
                // #endif
                
                // Divider().padding(.leading)
                Spacer()
            
                // 社群媒體圖示
                HStack(spacing: CGFloat(20).auto()) {
                    // Facebook 按鈕
                    Button {
                        // 打開 Facebook
                        if let url = URL(string: "https://x.com/minlsm_com") {
                            UIApplication.shared.open(url)
                        }
                    } label: {
                        AppIconsSymbol.createView(for: AppIcons.facebook, fontSize: CGFloat(44).auto(), color: HexColor.color("222222")) // 使用 scrollDown 圖示
                    }
                    
                    // Instagram 按鈕
                    Button {
                        // 打開 Instagram
                        if let url = URL(string: "https://www.instagram.com/minlsm_com") {
                            UIApplication.shared.open(url)
                        }
                    } label: {
                        AppIconsSymbol.createView(for: AppIcons.instagram, fontSize: CGFloat(44).auto(), color: HexColor.color("222222")) // 使用 scrollDown 圖示
                    }
                }
                .padding(.bottom, CGFloat(50).auto())
            }
            .background(HexColor.color("F9F9F9"))
            .cornerRadius(CGFloat(10).auto())
            .padding(.horizontal, displayAreaPadding)
            
            
        }
        // .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(HexColor.color("F9F9F9"))
        .edgesIgnoringSafeArea(.all)
        .sheet(isPresented: $showingHistory) {
            // Sheet消失後的回調 - 在這裡執行滾動操作
            DispatchQueue.main.async {
                scrollToTopAction()
            }
        } content: {
            HistoryView(viewModel: viewModel, onSelectHistory: {
                // 在選擇歷史項目時，只關閉歷史視圖
                // 滾動操作會在 sheet 完全關閉後執行
                showingHistory = false
            })
            .interactiveDismissDisabled(true) // 禁用滑動關閉
        }
        .sheet(isPresented: $showingNumberFormat) {
            // 數字格式資訊頁面
            NumberFormatTestView()
            .interactiveDismissDisabled(true) // 禁用滑動關閉
        }
        .sheet(isPresented: $showingProIntro) {
            // PRO介紹頁面
            ProIntroductionView()
            .interactiveDismissDisabled(true) // 禁用滑動關閉
        }
        .sheet(isPresented: $showingHapticSettings) {
            // 觸覺反饋設定頁面
            HapticSettingsView()
            .interactiveDismissDisabled(true) // 禁用滑動關閉
        }
        .onReceive(languageService.$currentLanguage) { _ in
            // 當語言變更時強制刷新視圖
            // SwiftUI 會自動重新渲染使用 .localized 的文字
        }
    }
}

// MARK: - 設定列視圖
struct SettingsRow: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .foregroundColor(HexColor.color("222222"))
                    .font(.system(size: CGFloat(32).auto(), weight: .semibold, design: .rounded))
                Spacer()
                // Image(systemName: "chevron.right")
                //     .foregroundColor(.secondary)
            }
            // .padding(.vertical, CGFloat(10).auto())
            // .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 帶副標題的設定列視圖
struct SettingsRowWithSubtitle: View {
    let title: String
    let subtitle: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .foregroundColor(HexColor.color("222222"))
                        .font(.system(size: CGFloat(32).auto(), weight: .semibold, design: .rounded))
                    Text(subtitle)
                        .foregroundColor(HexColor.color("666666"))
                        .font(.system(size: CGFloat(20).auto(), weight: .regular, design: .rounded))
                }
                Spacer()
            }
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 預覽
#Preview {
    SettingsPageView(viewModel: CalculatorViewModel()) {
        print("Scroll to top")
    }
} 
