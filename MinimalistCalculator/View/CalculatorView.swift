// CalculatorView.swift
// 主計算器視圖，整合顯示區域和按鈕網格

import SwiftUI
import AutoInch

struct CalculatorView: View {
    // MARK: - 屬性
    @ObservedObject var viewModel: CalculatorViewModel
    @ObservedObject private var numberFormatService = NumberFormatService.shared
    @ObservedObject private var hapticService = HapticService.shared
    
    // 佈局調整參數 - 可微調
    @State private var displayAreaPadding: CGFloat = CGFloat(8).auto()
    @State private var horizontalAlignment: CGFloat = CGFloat(35).auto() // 0表示完全對齊，正值表示向右偏移
    
    // 按鈕定義
    let buttons: [[ButtonItem]] = [
        [
            .init(type: .clear, color: HexColor.color("F9F9F9")),
            .init(type: .parenthesis(.left), color: HexColor.color("F9F9F9")),
            .init(type: .parenthesis(.right), color: HexColor.color("F9F9F9")),
            .init(type: .operation(.divide), color: HexColor.color("F9F9F9"))
        ],
        [
            .init(type: .digit(7), color: HexColor.color("F9F9F9")),
            .init(type: .digit(8), color: HexColor.color("F9F9F9")),
            .init(type: .digit(9), color: HexColor.color("F9F9F9")),
            .init(type: .operation(.multiply), color: HexColor.color("F9F9F9"))
        ],
        [
            .init(type: .digit(4), color: HexColor.color("F9F9F9")),
            .init(type: .digit(5), color: HexColor.color("F9F9F9")),
            .init(type: .digit(6), color: HexColor.color("F9F9F9")),
            .init(type: .operation(.subtract), color: HexColor.color("F9F9F9"))
        ],
        [
            .init(type: .digit(1), color: HexColor.color("F9F9F9")),
            .init(type: .digit(2), color: HexColor.color("F9F9F9")),
            .init(type: .digit(3), color: HexColor.color("F9F9F9")),
            .init(type: .operation(.add), color: HexColor.color("F9F9F9"))
        ],
        [
            .init(type: .backspace, color: HexColor.color("F9F9F9")),
            .init(type: .digit(0), color: HexColor.color("F9F9F9")),
            .init(type: .decimal, color: HexColor.color("F9F9F9")),
            .init(type: .equal, color: HexColor.color("F9F9F9"))
        ]
    ]
    
    // 初始化方法
    init(viewModel: CalculatorViewModel) {
        self.viewModel = viewModel
    }
    
    // MARK: - 視圖
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: CGFloat(12).auto()) {
                // 🔄 簡化：使用新的顯示區域
                NewDisplayAreaView(
                    inputExpression: $viewModel.inputExpression,
                    displayValue: viewModel.displayValue
                )
                // .padding(.horizontal, displayAreaPadding)
                .padding(.trailing, CGFloat(60).auto())
                
                // 🚫 舊架構：註解掉舊的顯示區域
                /*
                DisplayAreaView(
                    inputExpression: viewModel.formattedInputExpression,
                    displayValue: viewModel.displayValue
                )
                .padding(.horizontal, displayAreaPadding)
                .padding(.trailing, horizontalAlignment)
                */
                
                // 按鈕網格
                VStack(spacing: CGFloat(12).auto()) {
                    ForEach(buttons.indices, id: \.self) { rowIndex in
                        HStack(spacing: CGFloat(12).auto()) {
                            ForEach(buttons[rowIndex].indices, id: \.self) { colIndex in
                                let buttonItem = buttons[rowIndex][colIndex]
                                
                                if buttonItem.isWide {
                                    // 寬按鈕 (如果有)
                                    Button {
                                        handleButtonTap(buttonType: buttonItem.type)
                                    } label: {
                                        ZStack {
                                            Capsule()
                                                .fill(buttonItem.color)
                                            
                                            AppIconsSymbol.createView(
                                                for: buttonItem.type.iconString,
                                                fontSize: CGFloat(30).auto(),
                                                color: HexColor.color("222222")
                                            )
                                        }
                                    }
                                    .frame(height: CGFloat(60).auto())
                                    .frame(maxWidth: .infinity)
                                } else {
                                    // 普通按鈕
                                    CalculatorButtonView(
                                        iconString: buttonItem.type.iconString,
                                        buttonColor: buttonItem.color,
                                        textColor: HexColor.color("222222"),
                                        fontSize: CGFloat(47).auto(),
                                        action: {
                                            handleButtonTap(buttonType: buttonItem.type)
                                        }
                                    )
                                    .frame(height: CGFloat(60).auto())
                                }
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                // .padding(.horizontal, horizontalAlignment)
            }
            .padding(.horizontal, CGFloat(0).auto())
            .background(HexColor.color("F9F9F9"))
            .frame(width: geometry.size.width, height: geometry.size.height, alignment: .center)
        }
        .background(HexColor.color("F9F9F9"))
        .edgesIgnoringSafeArea(.all) // 使用 edgesIgnoringSafeArea
    }
    
    // MARK: - 輔助方法
    
    /// 處理按鈕點擊，包含觸覺反饋
    private func handleButtonTap(buttonType: ButtonType) {
        // 根據按鈕類型提供不同強度的觸覺反饋
        switch buttonType {
        case .digit(_):
            // 數字按鈕使用 medium 強度
            hapticService.triggerDigitHaptic()
        case .equal, .clear:
            // equal 和 clear 按鈕使用 heavy 強度
            hapticService.triggerActionHaptic()
        case .backspace:
            // backspace 按鈕使用 light 強度
            hapticService.triggerBackspaceHaptic()
        default:
            // 其他符號按鈕使用 soft 強度
            hapticService.triggerSymbolHaptic()
        }
        
        // 執行原本的按鈕邏輯
        viewModel.buttonTapped(buttonType: buttonType)
    }
}

// MARK: - 輔助類型
// 按鈕項目結構
struct ButtonItem {
    let type: ButtonType
    let color: Color
    let isWide: Bool
    
    init(type: ButtonType, color: Color = HexColor.color("F9F9F9"), isWide: Bool = false) {
        self.type = type
        self.color = color
        self.isWide = isWide
    }
}

// MARK: - 預覽
#Preview {
    CalculatorView(viewModel: CalculatorViewModel())
} 
