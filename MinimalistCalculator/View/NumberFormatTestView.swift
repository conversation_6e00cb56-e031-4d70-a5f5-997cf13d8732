// NumberFormatTestView.swift
// 數字格式測試視圖，用於展示系統數字格式偵測功能

import SwiftUI

struct NumberFormatTestView: View {
    @StateObject private var numberFormatService = NumberFormatService.shared
    @State private var testNumber: Double = 1234567.89
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 標題
                Text("數字格式偵測")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // 當前偵測到的格式
                VStack(alignment: .leading, spacing: 10) {
                    Text("偵測到的系統格式:")
                        .font(.headline)
                    
                    Text(numberFormatService.getFormatExample())
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                        .padding()
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(10)
                }
                .padding(.horizontal)
                
                // 分隔符資訊
                VStack(alignment: .leading, spacing: 8) {
                    Text("分隔符設定:")
                        .font(.headline)
                    
                    HStack {
                        Text("千位分隔符:")
                        Text("'\(numberFormatService.thousandsSeparator)'")
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    HStack {
                        Text("小數分隔符:")
                        Text("'\(numberFormatService.decimalSeparator)'")
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                    }
                }
                .padding(.horizontal)
                
                Divider()
                
                // 測試數字輸入
                VStack(alignment: .leading, spacing: 10) {
                    Text("測試數字:")
                        .font(.headline)
                    
                    HStack {
                        Text("原始數字:")
                        Spacer()
                        Text("\(testNumber, specifier: "%.6f")")
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("NumberFormatter:")
                        Spacer()
                        Text(numberFormatService.formatNumberWithFormatter(testNumber))
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    }
                    
                    HStack {
                        Text("formatNumber:")
                        Spacer()
                        Text(numberFormatService.formatNumber(String(testNumber)))
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    // 新增：模擬 updateDisplayValue 的邏輯
                    HStack {
                        Text("模擬 updateDisplayValue:")
                        Spacer()
                        Text(simulateUpdateDisplayValue(String(testNumber)))
                            .fontWeight(.semibold)
                            .foregroundColor(.purple)
                    }
                    
                    // 新增：模擬完整計算流程
                    VStack(alignment: .leading, spacing: 5) {
                        Text("模擬計算流程:")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        HStack {
                            Text("1. 原始結果:")
                            Spacer()
                            Text(String(testNumber))
                                .fontWeight(.semibold)
                                .foregroundColor(.gray)
                        }
                        
                        HStack {
                            Text("2. formatResult:")
                            Spacer()
                            Text(numberFormatService.formatNumberWithFormatter(testNumber))
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)
                        }
                        
                        HStack {
                            Text("3. updateDisplayValue:")
                            Spacer()
                            Text(simulateUpdateDisplayValue(numberFormatService.formatNumberWithFormatter(testNumber)))
                                .fontWeight(.semibold)
                                .foregroundColor(.purple)
                        }
                        
                        HStack {
                            Text("4. currentNumber:")
                            Spacer()
                            Text(String(testNumber))
                                .fontWeight(.semibold)
                                .foregroundColor(.orange)
                        }
                    }
                    .padding(.top, 10)
                    .padding(.horizontal, 10)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.horizontal)
                
                // 測試數字選擇器
                VStack {
                    Text("選擇測試數字:")
                        .font(.subheadline)
                    
                    Picker("測試數字", selection: $testNumber) {
                        Text("1.1").tag(1.1)
                        Text("42").tag(42.0)
                        Text("999.99").tag(999.99)
                        Text("1,000").tag(1000.0)
                        Text("1,234,567.89").tag(1234567.89)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    // 添加特殊測試案例
                    VStack(alignment: .leading, spacing: 8) {
                        Text("特殊測試案例:")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        HStack {
                            Text("輸入 '1.':")
                            Spacer()
                            Text(numberFormatService.formatNumber("1."))
                                .fontWeight(.semibold)
                                .foregroundColor(.red)
                        }
                        
                        HStack {
                            Text("輸入 '0.':")
                            Spacer()
                            Text(numberFormatService.formatNumber("0."))
                                .fontWeight(.semibold)
                                .foregroundColor(.red)
                        }
                        
                        HStack {
                            Text("輸入 '123.':")
                            Spacer()
                            Text(numberFormatService.formatNumber("123."))
                                .fontWeight(.semibold)
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.top, 10)
                    .padding(.horizontal, 10)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                    
                    // 新增：退格測試區域
                    VStack(alignment: .leading, spacing: 8) {
                        Text("退格測試 (spaceDot/spaceComma):")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        
                        HStack {
                            Text("模擬輸入 '12345':")
                            Spacer()
                            Text(simulateBackspaceTest())
                                .fontWeight(.semibold)
                                .foregroundColor(.blue)
                        }
                        
                        Text("測試步驟：輸入 12345 → 格式化為 12 345 → 退格一次 → 應該顯示 1 234")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    .padding(.top, 10)
                    .padding(.horizontal, 10)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding(.horizontal)
                
                Spacer()
                
                // 系統資訊
                VStack(alignment: .leading, spacing: 5) {
                    Text("系統資訊:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("Locale: \(Locale.current.identifier)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let regionCode = Locale.current.regionCode {
                        Text("地區: \(regionCode)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let languageCode = Locale.current.languageCode {
                        Text("語言: \(languageCode)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarHidden(true)
            
            // 手動刷新按鈕（用於測試）
            Button("手動刷新數字格式") {
                NumberFormatService.shared.refreshNumberFormat()
            }
            .padding()
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
            
            Text("點擊上方按鈕可以手動觸發數字格式刷新，模擬 app 從背景回到前景的情況")
                .font(.caption)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding()
        }
        .navigationTitle("數字格式測試")
    }
    
    // 模擬 updateDisplayValue 的邏輯
    private func simulateUpdateDisplayValue(_ number: String) -> String {
        // 如果是錯誤消息，直接顯示
        if number.lowercased().contains("error") {
            return number
        }
        
        // 檢查是否已經是格式化的數字（包含千位分隔符）
        let hasThousandsSeparator = number.contains(numberFormatService.thousandsSeparator)
        let hasSystemDecimalSeparator = number.contains(numberFormatService.decimalSeparator)
        
        // 如果已經包含格式化分隔符，直接使用，否則進行格式化
        let formattedNumber: String
        if hasThousandsSeparator || (hasSystemDecimalSeparator && numberFormatService.decimalSeparator != ".") {
            // 已經格式化過，直接使用
            formattedNumber = number
        } else {
            // 需要格式化
            formattedNumber = numberFormatService.formatNumber(number)
        }
        
        return formattedNumber
    }
    
    // 模擬退格測試
    private func simulateBackspaceTest() -> String {
        // 模擬輸入 12345，然後格式化
        let originalNumber = "12345"
        let formattedNumber = numberFormatService.formatNumber(originalNumber)
        
        // 模擬退格一次（移除最後一個數字）
        let cleanNumber = numberFormatService.parseFormattedNumber(formattedNumber)
        let afterBackspace = String(cleanNumber.dropLast())
        let reformattedAfterBackspace = numberFormatService.formatNumber(afterBackspace)
        
        return "原始: \(originalNumber) → 格式化: \(formattedNumber) → 退格後: \(reformattedAfterBackspace)"
    }
}

struct NumberFormatTestView_Previews: PreviewProvider {
    static var previews: some View {
        NumberFormatTestView()
    }
} 