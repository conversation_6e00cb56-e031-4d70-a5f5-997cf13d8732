// HapticSettingsView.swift
// 觸覺反饋設定視圖

import SwiftUI
import AutoInch

struct HapticSettingsView: View {
    // MARK: - 屬性
    @ObservedObject private var hapticService = HapticService.shared
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - 視圖
    var body: some View {
        ZStack(alignment: .top) {
            // 背景
            HexColor.color("F9F9F9")
                .edgesIgnoringSafeArea(.all)
            
            VStack(alignment: .leading, spacing: 0) {
                // 自定義頂部工具欄
                HStack {
                    // 關閉按鈕
                    Button(action: {
                        dismiss()
                    }) {
                        AppIconsSymbol.createView(
                            for: AppIcons.close, 
                            fontSize: CGFloat(44).auto(), 
                            color: HexColor.color("222222")
                        )
                    }
                    
                    Spacer()
                }
                .padding(.top, CGFloat(40).auto())
                .padding(.horizontal, CGFloat(40).auto())
            
                Spacer() // 頂部推力
                // 設定選項
                HStack(spacing: CGFloat(40).auto()) {
                    // ON 選項
                    HapticOptionRow(
                        title: "haptic_on".localized,
                        isSelected: hapticService.isHapticEnabled,
                        action: {
                            hapticService.setHapticEnabled(true)
                            // 觸發一個測試觸覺反饋
                            hapticService.triggerActionHaptic()
                        }
                    )
                    
                    // OFF 選項
                    HapticOptionRow(
                        title: "haptic_off".localized,
                        isSelected: !hapticService.isHapticEnabled,
                        action: {
                            hapticService.setHapticEnabled(false)
                        }
                    )
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, CGFloat(60).auto())
                
                Spacer()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity) // 移除 alignment: .top 讓內容可以垂直居中
        }
    }
}

// MARK: - 觸覺反饋選項行視圖
struct HapticOptionRow: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.system(size: CGFloat(28).auto(), weight: .medium, design: .rounded))
                    .foregroundColor(HexColor.color("222222"))
                    .opacity(isSelected ? 1.0 : 0.5) // 根據 isSelected 切換透明度
                
                // Spacer()
                
                // // 選中指示器
                // if isSelected {
                //     AppIconsSymbol.createView(
                //         for: AppIcons.check, 
                //         fontSize: CGFloat(24).auto(), 
                //         color: HexColor.color("222222")
                //     )
                // }
            }
            .padding(.vertical, CGFloat(15).auto())
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 預覽
#Preview {
    HapticSettingsView()
} 