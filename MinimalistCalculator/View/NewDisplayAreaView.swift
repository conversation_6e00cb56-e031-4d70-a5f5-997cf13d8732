// NewDisplayAreaView.swift
// 簡化方案：inputExpression 添加簡單 overlay 千位符，displayValue 格式化

import SwiftUI
import UIKit
import AutoInch

struct NewDisplayAreaView: View {
    // MARK: - 屬性
    @Binding var inputExpression: String
    let displayValue: String
    var horizontalPadding: CGFloat = CGFloat(0).auto()
    
    // 數字格式服務
    private let numberFormatService = NumberFormatService.shared
    
    // 檢測 ScrollView 是否有偏移
    @State private var inputHasScrolled: Bool = false
    @State private var displayHasScrolled: Bool = false
    @State private var inputIsAtOrigin: Bool = true
    @State private var displayIsAtOrigin: Bool = true
    
    // MARK: - 視圖
    var body: some View {
        VStack(alignment: .trailing, spacing: CGFloat(0).auto()) {
            // 輸入表達式區域 - 直接顯示格式化文本
            GeometryReader { geometry in
                ScrollViewReader { proxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            // 直接顯示格式化的 TextField
                            SimpleTextField(text: $inputExpression)
                                .frame(height: CGFloat(30).auto())
                                .id("textfield")
                        }
                        .fixedSize(horizontal: true, vertical: false)
                        .frame(minWidth: geometry.size.width - horizontalPadding * 2, alignment: .trailing)
                    }
                    .frame(height: CGFloat(30).auto())
                    .padding(.trailing, CGFloat(4).auto()) // 添加右邊距
                    // .padding(.horizontal, horizontalPadding)
                    .simultaneousGesture(
                        DragGesture()
                            .onChanged { _ in
                                // 開始滾動，離開原點
                                inputIsAtOrigin = false
                                inputHasScrolled = true
                            }
                            .onEnded { _ in
                                // 滾動結束後延遲檢查是否回到原點
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                                    // 自動滾動回原點檢測
                                    withAnimation(.easeInOut(duration: 0.2)) {
                                        proxy.scrollTo("textfield", anchor: .trailing)
                                    }
                                    // 延遲檢查是否真的回到原點
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                        inputIsAtOrigin = true
                                        inputHasScrolled = false
                                    }
                                }
                            }
                    )
                    .overlay(
                        // 右邊淡出漸層
                        HStack {
                            Spacer()
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: HexColor.color("F9F9F9").opacity(0), location: 0),
                                    .init(color: HexColor.color("F9F9F9").opacity(0.8), location: 0.7),
                                    .init(color: HexColor.color("F9F9F9"), location: 1)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                            .frame(width: CGFloat(20).auto())
                            .opacity(inputHasScrolled && !inputIsAtOrigin ? 1 : 0)
                            .animation(.easeInOut(duration: 0.2), value: inputHasScrolled)
                        }
                        .allowsHitTesting(false),
                        alignment: .trailing
                    )
                    .onChange(of: inputExpression) { _ in
                        // 當文字改變時，滾動到右邊（顯示個位數）
                        withAnimation(.easeInOut(duration: 0.2)) {
                            proxy.scrollTo("textfield", anchor: .trailing)
                        }
                        // 重置滾動狀態，回到原點
                        inputHasScrolled = false
                        inputIsAtOrigin = true

                    }
                    .onAppear {
                        // 初始時滾動到右邊 - 使用延遲確保 iOS 15+ 正常工作
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1800.1) {
                            proxy.scrollTo("textfield", anchor: .trailing)
                        }
                    }
                }
            }
            .frame(height: CGFloat(30).auto())
            
            // 計算結果區域 - 支援橫向滾動，使用格式化
            GeometryReader { geometry in
                ScrollViewReader { proxy in
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            Text(numberFormatService.formatForDisplay(displayValue))
                                .font(.system(size: CGFloat(60).auto(), weight: .regular, design: .rounded))
                                .foregroundColor(HexColor.color("222222"))
                                .lineLimit(1)
                                .frame(height: CGFloat(60).auto())
                                .fixedSize(horizontal: true, vertical: false)
                                .id("displaytext")
                        }
                        .frame(minWidth: geometry.size.width - horizontalPadding * 2, alignment: .trailing)
                    }
                    .frame(height: CGFloat(60).auto())
                    .padding(.horizontal, horizontalPadding)
                    .simultaneousGesture(
                        DragGesture()
                            .onChanged { _ in
                                // 開始滾動，離開原點
                                displayIsAtOrigin = false
                                displayHasScrolled = true
                            }
                            .onEnded { _ in
                                // 滾動結束後延遲檢查是否回到原點
                                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                                    // 自動滾動回原點檢測
                                    withAnimation(.easeInOut(duration: 0.2)) {
                                        proxy.scrollTo("displaytext", anchor: .trailing)
                                    }
                                    // 延遲檢查是否真的回到原點
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                        displayIsAtOrigin = true
                                        displayHasScrolled = false
                                    }
                                }
                            }
                    )
                    .overlay(
                        // 右邊淡出漸層
                        HStack {
                            Spacer()
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: HexColor.color("F9F9F9").opacity(0), location: 0),
                                    .init(color: HexColor.color("F9F9F9").opacity(0.8), location: 0.7),
                                    .init(color: HexColor.color("F9F9F9"), location: 1)
                                ]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                            .frame(width: CGFloat(30).auto())
                            .opacity(displayHasScrolled && !displayIsAtOrigin ? 1 : 0)
                            .animation(.easeInOut(duration: 0.2), value: displayHasScrolled)
                        }
                        .allowsHitTesting(false),
                        alignment: .trailing
                    )
                    .onChange(of: displayValue) { _ in
                        // 當顯示值改變時，滾動到右邊（顯示個位數）
                        withAnimation(.easeInOut(duration: 0.2)) {
                            proxy.scrollTo("displaytext", anchor: .trailing)
                        }
                        // 重置滾動狀態，回到原點
                        displayHasScrolled = false
                        displayIsAtOrigin = true

                    }
                    .onAppear {
                        // 初始時滾動到右邊 - 使用延遲確保 iOS 15+ 正常工作
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1800.1) {
                            proxy.scrollTo("displaytext", anchor: .trailing)
                        }
                    }
                }
            }
            .frame(height: CGFloat(60).auto())
        }
        .background(HexColor.color("F9F9F9"))
        .onAppear {
            // 當視圖出現時，重新激活 TextField 游標
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                SimpleTextField.currentTextField?.becomeFirstResponder()
            }
        }
    }
    
    // 移除格式化，獲取原始表達式
    private func removeFormatting(_ formattedExpression: String) -> String {
        // 移除千位分隔符
        let numberFormatService = NumberFormatService.shared
        return formattedExpression.replacingOccurrences(of: numberFormatService.thousandsSeparator, with: "")
    }
}

// MARK: - 簡單的 TextField，支援按鈕插入和完全編輯
struct SimpleTextField: UIViewRepresentable {
    @Binding var text: String
    
    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        textField.delegate = context.coordinator
        textField.font = {
            if let descriptor = UIFontDescriptor.preferredFontDescriptor(withTextStyle: .body)
                .withDesign(.rounded) {
                return UIFont(descriptor: descriptor, size: CGFloat(24).auto())
            } else {
                return UIFont.systemFont(ofSize: CGFloat(24).auto(), weight: .regular)
            }
        }()
        textField.textColor = UIColor(red: 0.43, green: 0.43, blue: 0.43, alpha: 1.0) // 顯示可見文字
        textField.backgroundColor = UIColor.clear
        textField.borderStyle = .none
        textField.textAlignment = .right
        textField.autocorrectionType = .no
        
        // ✅ 禁用鍵盤但保持游標
        textField.inputView = UIView() // 隱藏鍵盤
        textField.inputAccessoryView = UIView() // 隱藏工具列
        
        textField.tintColor = UIColor(red: 0.43, green: 0.43, blue: 0.43, alpha: 1.0)
        textField.adjustsFontSizeToFitWidth = false
        textField.minimumFontSize = CGFloat(24).auto()
        
        // 設置當前 TextField 引用
        SimpleTextField.currentTextField = textField
        
        // ✅ 讓 TextField 成為第一響應者，顯示游標
        DispatchQueue.main.async {
            textField.becomeFirstResponder()
        }
        
        return textField
    }
    
    func updateUIView(_ uiView: UITextField, context: Context) {
        SimpleTextField.currentTextField = uiView
        
        // 顯示格式化的文字
        let formattedText = formatExpressionForTextField(text)
        if uiView.text != formattedText {
            uiView.text = formattedText
        }
        
        // 確保 TextField 保持第一響應者狀態（顯示游標）
        if !uiView.isFirstResponder {
            DispatchQueue.main.async {
                uiView.becomeFirstResponder()
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UITextFieldDelegate {
        let parent: SimpleTextField
        
        init(_ parent: SimpleTextField) {
            self.parent = parent
            super.init()
            
            // 監聽按鈕點擊通知
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleInsertText),
                name: .insertTextAtCursor,
                object: nil
            )
            
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(handleDeleteText),
                name: .deleteAtCursor,
                object: nil
            )
        }
        
        deinit {
            NotificationCenter.default.removeObserver(self)
        }
        
        @objc func handleInsertText(_ notification: Notification) {
            guard let textToInsert = notification.object as? String,
                  let textField = SimpleTextField.currentTextField else {
                // 備用方案：直接在字符串末尾添加
                parent.text += notification.object as? String ?? ""
                // 觸發顯示值更新
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .updateDisplayValue, object: nil)
                }
                return
            }
            
            // 獲取當前游標在格式化文本中的位置
            let formattedCursorPosition = getCursorPosition(in: textField)
            let formattedText = textField.text ?? ""
            
            // 將格式化文本的游標位置映射到原始文本位置
            let rawCursorPosition = mapFormattedPositionToRaw(
                formattedPosition: formattedCursorPosition,
                formattedText: formattedText,
                rawText: parent.text
            )
            
            // 在原始文本的游標位置插入文字
            let safePosition = min(rawCursorPosition, parent.text.count)
            let index = parent.text.index(parent.text.startIndex, offsetBy: safePosition)
            parent.text.insert(contentsOf: textToInsert, at: index)
            
            // 觸發顯示值更新
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: .updateDisplayValue, object: nil)
            }
            
            // 計算新的游標位置並設置
            DispatchQueue.main.async {
                let newFormattedText = self.parent.formatExpressionForTextField(self.parent.text)
                textField.text = newFormattedText
                
                let newRawPosition = safePosition + textToInsert.count
                let newFormattedPosition = self.mapRawPositionToFormatted(
                    rawPosition: newRawPosition,
                    rawText: self.parent.text,
                    formattedText: newFormattedText
                )
                
                self.setCursorPosition(in: textField, position: newFormattedPosition)
            }
        }
        
        @objc func handleDeleteText(_ notification: Notification) {
            guard let textField = SimpleTextField.currentTextField else {
                // 備用方案：直接刪除最後一個字符
                if !parent.text.isEmpty {
                    parent.text.removeLast()
                }
                // 觸發顯示值更新
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .updateDisplayValue, object: nil)
                }
                return
            }
            
            // 獲取當前游標在格式化文本中的位置
            let formattedCursorPosition = getCursorPosition(in: textField)
            let formattedText = textField.text ?? ""
            
            // 將格式化文本的游標位置映射到原始文本位置
            let rawCursorPosition = mapFormattedPositionToRaw(
                formattedPosition: formattedCursorPosition,
                formattedText: formattedText,
                rawText: parent.text
            )
            
            let safePosition = min(rawCursorPosition, parent.text.count)
            
            guard safePosition > 0 && !parent.text.isEmpty else { return }
            
            // 刪除原始文本中游標前的字符
            let index = parent.text.index(parent.text.startIndex, offsetBy: safePosition - 1)
            parent.text.remove(at: index)
            
            // 觸發顯示值更新
            DispatchQueue.main.async {
                NotificationCenter.default.post(name: .updateDisplayValue, object: nil)
            }
            
            // 計算新的游標位置並設置
            DispatchQueue.main.async {
                let newFormattedText = self.parent.formatExpressionForTextField(self.parent.text)
                textField.text = newFormattedText
                
                let newRawPosition = safePosition - 1
                let newFormattedPosition = self.mapRawPositionToFormatted(
                    rawPosition: newRawPosition,
                    rawText: self.parent.text,
                    formattedText: newFormattedText
                )
                
                self.setCursorPosition(in: textField, position: newFormattedPosition)
            }
        }
        
        func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
            // 處理用戶直接編輸入
            if let text = textField.text,
               let textRange = Range(range, in: text) {
                let updatedFormattedText = text.replacingCharacters(in: textRange, with: string)
                
                // 將格式化文本轉換回原始文本（只需要移除千位分隔符）
                let numberFormatService = NumberFormatService.shared
                let updatedRawText = updatedFormattedText
                    .replacingOccurrences(of: numberFormatService.thousandsSeparator, with: "")
                
                parent.text = updatedRawText
                
                // 觸發顯示值更新
                DispatchQueue.main.async {
                    NotificationCenter.default.post(name: .updateDisplayValue, object: nil)
                }
                
                // 返回 false，因為我們會在 updateUIView 中更新顯示
                return false
            }
            return true
        }
        
        func textFieldDidChangeSelection(_ textField: UITextField) {
            SimpleTextField.currentTextField = textField
        }
        
        // 輔助方法
        private func getCursorPosition(in textField: UITextField) -> Int {
            guard let selectedRange = textField.selectedTextRange else { return 0 }
            return textField.offset(from: textField.beginningOfDocument, to: selectedRange.start)
        }
        
        private func setCursorPosition(in textField: UITextField, position: Int) {
            let safePosition = min(position, textField.text?.count ?? 0)
            guard let newPosition = textField.position(from: textField.beginningOfDocument, offset: safePosition) else { return }
            textField.selectedTextRange = textField.textRange(from: newPosition, to: newPosition)
        }
        
        private func mapFormattedPositionToRaw(formattedPosition: Int, formattedText: String, rawText: String) -> Int {
            // 簡化邏輯：只需要計算千位分隔符的數量
            let numberFormatService = NumberFormatService.shared
            let separator = numberFormatService.thousandsSeparator
            
            let beforeCursor = String(formattedText.prefix(formattedPosition))
            let separatorCount = beforeCursor.components(separatedBy: separator).count - 1
            
            // 原始位置 = 格式化位置 - 分隔符數量
            return max(0, formattedPosition - separatorCount)
        }
        
        private func mapRawPositionToFormatted(rawPosition: Int, rawText: String, formattedText: String) -> Int {
            // 簡化邏輯：格式化原始文本的前綴來獲取對應位置
            let rawPrefix = String(rawText.prefix(rawPosition))
            let formattedPrefix = parent.formatExpressionForTextField(rawPrefix)
            
            return formattedPrefix.count
        }
    }
    
    // 格式化表達式用於 TextField 顯示
    func formatExpressionForTextField(_ expression: String) -> String {
        // 現在底層數據已經沒有空格，只需要格式化數字部分
        var result = ""
        var currentNumber = ""
        
        for char in expression {
            if char.isNumber || char == "." {
                // 累積數字字符
                currentNumber.append(char)
            } else {
                // 遇到操作符，先處理累積的數字
                if !currentNumber.isEmpty {
                    let formattedNumber = formatSingleNumber(currentNumber)
                    result.append(formattedNumber)
                    currentNumber = ""
                }
                // 添加操作符（保持原樣）
                result.append(char)
            }
        }
        
        // 處理最後的數字（如果有）
        if !currentNumber.isEmpty {
            let formattedNumber = formatSingleNumber(currentNumber)
            result.append(formattedNumber)
        }
        
        return result
    }
    
    // 格式化單個數字
    private func formatSingleNumber(_ numberString: String) -> String {
        // 檢查是否包含小數點
        if numberString.contains(".") {
            let parts = numberString.split(separator: ".", omittingEmptySubsequences: false)
            if parts.count >= 1 {
                let integerPart = String(parts[0])
                let decimalPart = parts.count > 1 ? String(parts[1]) : ""
                
                // 只格式化整數部分（如果超過3位）
                if integerPart.count > 3, let number = Int(integerPart) {
                    let formatter = NumberFormatter()
                    formatter.numberStyle = .decimal
                    formatter.locale = Locale.current
                    
                    if let formattedInteger = formatter.string(from: NSNumber(value: number)) {
                        // 使用系統的小數分隔符
                        let numberFormatService = NumberFormatService.shared
                        return "\(formattedInteger)\(numberFormatService.decimalSeparator)\(decimalPart)"
                    }
                } else {
                    // 整數部分不需要格式化，但仍需要轉換小數分隔符
                    let numberFormatService = NumberFormatService.shared
                    return "\(integerPart)\(numberFormatService.decimalSeparator)\(decimalPart)"
                }
            }
        } else {
            // 純整數，如果超過3位就格式化
            if numberString.count > 3, let number = Int(numberString) {
                let formatter = NumberFormatter()
                formatter.numberStyle = .decimal
                formatter.locale = Locale.current
                
                if let formattedNumber = formatter.string(from: NSNumber(value: number)) {
                    return formattedNumber
                }
            }
        }
        
        return numberString
    }
    
    // 靜態變量來存儲當前的 textField
    static var currentTextField: UITextField?
}

// MARK: - 預覽
#Preview {
    struct PreviewWrapper: View {
        @State private var inputExpression = "1 + 2 × 3"
        
        var body: some View {
            NewDisplayAreaView(
                inputExpression: $inputExpression,
                displayValue: "7"
            )
        }
    }
    
    return PreviewWrapper()
}

// MARK: - String Extension for width calculation
extension String {
    func widthOfString(usingFont font: UIFont) -> CGFloat {
        let fontAttributes = [NSAttributedString.Key.font: font]
        let size = self.size(withAttributes: fontAttributes)
        return size.width
    }
}



 
