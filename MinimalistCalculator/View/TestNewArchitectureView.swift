// TestNewArchitectureView.swift
// 測試新架構的功能

import SwiftUI

struct TestNewArchitectureView: View {
    @StateObject private var viewModel = CalculatorViewModel()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("新架構測試")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                // 顯示區域測試
                VStack(alignment: .leading, spacing: 10) {
                    Text("顯示區域:")
                        .font(.headline)
                    
                    NewDisplayAreaView(
                        inputExpression: $viewModel.inputExpression,
                        displayValue: viewModel.displayValue
                    )
                    .frame(height: 120)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(10)
                }
                
                // 狀態資訊
                VStack(alignment: .leading, spacing: 8) {
                    Text("狀態資訊:")
                        .font(.headline)
                    
                    HStack {
                        Text("輸入表達式:")
                        Text("'\(viewModel.inputExpression)'")
                            .fontWeight(.semibold)
                            .foregroundColor(.blue)
                    }
                    
                    HStack {
                        Text("顯示值:")
                        Text("'\(viewModel.displayValue)'")
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    HStack {
                        Text("游標位置:")
                        Text("\(viewModel.cursorPosition)")
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                    }
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(10)
                
                // 測試按鈕
                VStack(spacing: 10) {
                    Text("測試按鈕:")
                        .font(.headline)
                    
                    HStack(spacing: 10) {
                        Button("1") {
                            viewModel.buttonTapped(buttonType: .digit(1))
                        }
                        .buttonStyle(TestButtonStyle())
                        
                        Button("2") {
                            viewModel.buttonTapped(buttonType: .digit(2))
                        }
                        .buttonStyle(TestButtonStyle())
                        
                        Button("3") {
                            viewModel.buttonTapped(buttonType: .digit(3))
                        }
                        .buttonStyle(TestButtonStyle())
                        
                        Button("+") {
                            viewModel.buttonTapped(buttonType: .operation(.add))
                        }
                        .buttonStyle(TestButtonStyle())
                    }
                    
                    HStack(spacing: 10) {
                        Button(".") {
                            viewModel.buttonTapped(buttonType: .decimal)
                        }
                        .buttonStyle(TestButtonStyle())
                        
                        Button("=") {
                            viewModel.buttonTapped(buttonType: .equal)
                        }
                        .buttonStyle(TestButtonStyle())
                        
                        Button("C") {
                            viewModel.buttonTapped(buttonType: .clear)
                        }
                        .buttonStyle(TestButtonStyle())
                        
                        Button("⌫") {
                            viewModel.buttonTapped(buttonType: .backspace)
                        }
                        .buttonStyle(TestButtonStyle())
                    }
                }
                
                // 官方格式化測試
                VStack(alignment: .leading, spacing: 8) {
                    Text("官方格式化測試:")
                        .font(.headline)
                    
                    let testNumber = 1234567.89
                    HStack {
                        Text("原始數字:")
                        Text("\(testNumber)")
                            .fontWeight(.semibold)
                    }
                    
                    HStack {
                        Text("官方格式化:")
                        Text(testNumber.formatted(.number.locale(Locale.current)))
                            .fontWeight(.semibold)
                            .foregroundColor(.purple)
                    }
                }
                .padding()
                .background(Color.purple.opacity(0.1))
                .cornerRadius(10)
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
}

struct TestButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(width: 50, height: 50)
            .background(Color.blue)
            .foregroundColor(.white)
            .cornerRadius(8)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
    }
}

struct TestNewArchitectureView_Previews: PreviewProvider {
    static var previews: some View {
        TestNewArchitectureView()
    }
} 