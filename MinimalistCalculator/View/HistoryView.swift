// HistoryView.swift
// 歷史記錄視圖

import SwiftUI
import AutoInch

struct HistoryView: View {
    @ObservedObject private var iapService = IAPService.shared
    // MARK: - 屬性
    @ObservedObject var viewModel: CalculatorViewModel
    @Environment(\.dismiss) private var dismiss
    var onSelectHistory: (() -> Void)? // 點擊歷史項目時的回調函數
    
    // 初始化函數
    init(viewModel: CalculatorViewModel, onSelectHistory: (() -> Void)? = nil) {
        self.viewModel = viewModel
        self.onSelectHistory = onSelectHistory
    }
    
    // MARK: - 視圖
    var body: some View {
        ZStack(alignment: .top) {
            // 主要內容
            VStack(spacing: 0) {
                // 自定義頂部工具欄 - 取代系統toolbar
                HStack {
                    // 關閉按鈕
                    Button(action: {
                        dismiss()
                    }) {
                        AppIconsSymbol.createView(for: AppIcons.close, fontSize: CGFloat(44).auto(), color: HexColor.color("222222")) // 使用 scrollDown 圖示
                    }
                    
                    Spacer()
                    
                    // 清除按鈕
                    Button(action: {
                        viewModel.clearHistory()
                    }) {
                        AppIconsSymbol.createView(for: AppIcons.clearHistory, fontSize: CGFloat(44).auto(), color: HexColor.color("222222")) // 使用 scrollDown 圖示
                    }
                    .disabled(viewModel.historyEntries.isEmpty)
                }
                .padding(.top, CGFloat(40).auto()) // 這裡控制工具欄距離頂部的距離
                .padding(.horizontal, CGFloat(40).auto())
                // .padding(.bottom, CGFloat(20).auto())
                
                // 內容區域
                if viewModel.historyEntries.isEmpty {
                    // 沒有歷史記錄 - 將此移出 ScrollView
                    Spacer() // 頂部空間
                    Text("history_no_history".localized)
                        .font(.system(size: CGFloat(20).auto(), weight: .regular, design: .rounded))
                        .foregroundColor(HexColor.color("222222"))
                    Spacer() // 底部空間，推動文字到中央
                } else {
                    // 有歷史記錄時才使用 ScrollView
                    ZStack(alignment: .center) {
                        // 歷史記錄 ScrollView
                        ScrollView {
                            // 添加頂部間距來補償頂部漸層的高度
                            // Color.clear.frame(height: CGFloat(20).auto())
                            
                            // 顯示歷史記錄列表
                            LazyVStack(spacing: CGFloat(12).auto()) {
                                ForEach(viewModel.historyEntries) { entry in
                                    HistoryEntryRowView(
                                        entry: entry,
                                        onTap: {
                                            // 點擊歷史記錄項目
                                            viewModel.useHistoryEntry(entry)
                                            
                                            // 先調用回調函數，然後關閉視圖
                                            if let callback = onSelectHistory {
                                                callback()
                                            } else {
                                                // 如果沒有提供回調，則只關閉視圖
                                                dismiss()
                                            }
                                        },
                                        viewModel: viewModel
                                    )
                                }
                                if iapService.isEntitlementActive("pro") {
                                    Text("history_unlimited_available".localized)
                                        .font(.system(size: CGFloat(14).auto(), weight: .regular, design: .rounded))
                                        .foregroundColor(HexColor.color("6E6E6E"))
                                        .padding(.top, CGFloat(16).auto())
                                        .frame(maxWidth: .infinity, alignment: .center)
                                        .opacity(0)
                                } else {
                                    Text("history_upgrade_pro".localized)
                                        .font(.system(size: CGFloat(16).auto(), weight: .regular, design: .rounded))
                                        .foregroundColor(HexColor.color("888888"))
                                        .padding(.top, CGFloat(16).auto())
                                        .frame(maxWidth: .infinity, alignment: .center)
                                }
                            }
                            .padding(.horizontal, CGFloat(15).auto())
                            
                            // 添加底部間距來補償底部漸層的高度
                            Color.clear.frame(height: CGFloat(20).auto())
                        }
                        
                        // 頂部淡出漸層
                        VStack {
                            LinearGradient(
                                gradient: Gradient(colors: [HexColor.color("F9F9F9"), HexColor.color("F9F9F9").opacity(0)]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .frame(height: CGFloat(30).auto())
                            Spacer()
                        }
                        
                        // 底部淡出漸層
                        VStack {
                            Spacer()
                            LinearGradient(
                                gradient: Gradient(colors: [HexColor.color("F9F9F9").opacity(0), HexColor.color("F9F9F9")]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                            .frame(height: CGFloat(30).auto())
                        }
                    }
                }
            }
            .padding(.bottom, CGFloat(40).auto())
        }
        .edgesIgnoringSafeArea(.all) // 忽略安全區域以全螢幕顯示
        .background(HexColor.color("F9F9F9"))
    }
} 