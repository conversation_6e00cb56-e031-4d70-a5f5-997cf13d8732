// ButtonType.swift
// 計算機按鈕類型定義

import Foundation

/// 計算機按鈕類型
enum ButtonType {
    case digit(Int) // 數字按鈕 (0-9)
    case decimal // 小數點 (.)
    case operation(OperationType) // 運算符 (+, -, *, /)
    case equal // 等號 (=)
    case clear // 清除 (C)
    case backspace // 退格/刪除 (⌫)
    case parenthesis(ParenthesisType) // 括號 ( 或 )
    
    /// 獲取對應的AppIcons字符
    var iconString: String {
        switch self {
        case .digit(let number):
            switch number {
            case 0: return AppIcons.zero
            case 1: return AppIcons.one
            case 2: return AppIcons.two
            case 3: return AppIcons.three
            case 4: return AppIcons.four
            case 5: return AppIcons.five
            case 6: return AppIcons.six
            case 7: return AppIcons.seven
            case 8: return AppIcons.eight
            case 9: return AppIcons.nine
            default: return ""
            }
        case .decimal:
            // 根據系統的小數分隔符選擇圖標
            let numberFormatService = NumberFormatService.shared
            return numberFormatService.decimalSeparator == "," ? AppIcons.comma : AppIcons.decimal
        case .operation(let op):
            switch op {
            case .add: return AppIcons.plus
            case .subtract: return AppIcons.minus
            case .multiply: return AppIcons.multiply
            case .divide: return AppIcons.division
            }
        case .equal:
            return AppIcons.equal
        case .clear:
            return AppIcons.clear
        case .backspace:
            return AppIcons.backspace
        case .parenthesis(let type):
            switch type {
            case .left: return AppIcons.parenthesisLeft
            case .right: return AppIcons.parenthesisRight
            }
        }
    }
    
    /// 獲取按鈕的實際值（用於表達式）
    var value: String {
        switch self {
        case .digit(let number):
            return String(number)
        case .decimal:
            return "."
        case .operation(let op):
            switch op {
            case .add: return "+"
            case .subtract: return "-"
            case .multiply: return "*"
            case .divide: return "/"
            }
        case .equal:
            return "="
        case .clear:
            return "C"
        case .backspace:
            return "⌫"
        case .parenthesis(let type):
            switch type {
            case .left: return "("
            case .right: return ")"
            }
        }
    }
}

/// 運算符類型
enum OperationType {
    case add
    case subtract
    case multiply
    case divide
}

/// 括號類型
enum ParenthesisType {
    case left
    case right
} 