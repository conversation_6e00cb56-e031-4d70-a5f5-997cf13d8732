// HistoryEntry.swift
// 計算器歷史記錄條目模型

import Foundation

struct HistoryEntry: Identifiable, Codable {
    // MARK: - 屬性
    let id: UUID
    let expression: String // 計算表達式
    let result: String // 計算結果
    let date: Date // 計算時間
    
    // MARK: - 初始化
    init(expression: String, result: String, date: Date = Date()) {
        self.id = UUID()
        self.expression = expression
        self.result = result
        self.date = date
    }
    
    // MARK: - 格式化
    
    /// 獲取格式化的日期字符串（支援多語系12小時制）
    var formattedDate: String {
        let formatter = DateFormatter()
        
        // 根據當前語言設定 locale
        let currentLanguage = LanguageService.shared.currentLanguage
        switch currentLanguage {
        case .english:
            formatter.locale = Locale(identifier: "en_US")
        case .traditionalChinese:
            formatter.locale = Locale(identifier: "zh_Hant_TW")
        case .japanese:
            formatter.locale = Locale(identifier: "ja_JP")
        }
        
        // 設定12小時制格式
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        
        // 確保使用12小時制
        formatter.dateFormat = getLocalizedDateFormat(for: currentLanguage)
        
        return formatter.string(from: date)
    }
    
    /// 根據語言獲取本地化的日期格式
    private func getLocalizedDateFormat(for language: LanguageService.Language) -> String {
        switch language {
        case .english:
            return "M/d/yy, h:mm a"
        case .traditionalChinese:
            return "yyyy/M/d ah:mm"
        case .japanese:
            return "yyyy/M/d ah:mm"
        }
    }
} 