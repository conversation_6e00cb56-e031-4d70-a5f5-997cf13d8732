import Foundation
import RevenueCat
import StoreKit
import SwiftUI

// 定義通知名稱
extension Notification.Name {
    static let proStatusChanged = Notification.Name("proStatusChanged")
    static let iapServiceInitialized = Notification.Name("iapServiceInitialized")
}

/// IAP 服務類，處理應用內購買和 RevenueCat 整合
class IAPService: NSObject, ObservableObject {
    // MARK: - 單例設計模式
    static let shared = IAPService()
    
    // MARK: - 屬性
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    @Published var isPro: Bool = false
    
    // 用於監聽 CustomerInfo 更新的回調
    var customerInfoUpdateHandler: ((CustomerInfo) -> Void)?
    
    // API Keys - 使用環境變數或配置文件替換
    private let apiKey = "appl_zfwswLyAPEbLjUhwTTfaWTHdFvV" // TODO: 替換為你的 API Key
    
    // 訂閱權益 ID
    private let proEntitlementId = "pro"
    
    // MARK: - 初始化
    private override init() {
        super.init()
        configureRevenueCat()
        updateCustomerInfo()
    }
    
    // MARK: - RevenueCat 配置
    private func configureRevenueCat() {
        // 設置 RevenueCat 的 debug 日誌級別
        Purchases.logLevel = .info
        
        // 配置 RevenueCat SDK
        Purchases.configure(withAPIKey: apiKey)
        
        // 設置代理以接收更新
        Purchases.shared.delegate = self
        
        // 立即檢查購買狀態
        updateCustomerInfo()
    }
    
    // MARK: - 顧客信息更新
    func updateCustomerInfo() {
        updateCustomerInfo(forceRefresh: false)
    }
    
    /// 更新顧客信息
    /// - Parameter forceRefresh: 是否強制從服務器刷新，忽略快取
    func updateCustomerInfo(forceRefresh: Bool = false) {
        if forceRefresh {
            print("🔄 強制從服務器刷新訂閱狀態（忽略快取）")
            // 使用 syncPurchases 強制同步
            Purchases.shared.syncPurchases { [weak self] (customerInfo, error) in
                self?.handleCustomerInfoUpdate(customerInfo: customerInfo, error: error, isForceRefresh: true)
            }
        } else {
            print("📱 從快取獲取訂閱狀態")
            Purchases.shared.getCustomerInfo { [weak self] (customerInfo, error) in
                self?.handleCustomerInfoUpdate(customerInfo: customerInfo, error: error, isForceRefresh: false)
            }
        }
    }
    
    /// 處理顧客信息更新的統一方法
    private func handleCustomerInfoUpdate(customerInfo: CustomerInfo?, error: Error?, isForceRefresh: Bool) {
        if let error = error {
            print("❌ 獲取顧客信息錯誤: \(error.localizedDescription)")
            return
        }
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            self.customerInfo = customerInfo
            
            // 詳細的訂閱狀態日誌輸出
            print("📊 數據來源: \(isForceRefresh ? "服務器強制刷新" : "本地快取")")
            self.logSubscriptionStatus(customerInfo)
            
            // 檢查 Pro 訂閱狀態
            self.updateProStatus(with: customerInfo)
            
            // 發送通知，確保所有依賴 IAPService 的組件都能更新
            NotificationCenter.default.post(name: .iapServiceInitialized, object: nil)
        }
    }
    
    // MARK: - 訂閱狀態日誌
    
    /// 詳細記錄訂閱狀態信息
    /// - Parameter customerInfo: 顧客信息
    private func logSubscriptionStatus(_ customerInfo: CustomerInfo?) {
        print("=== 訂閱狀態詳細信息 ===")
        print("🕐 檢查時間: \(Date())")
        
        guard let info = customerInfo else {
            print("❌ 無法獲取顧客信息")
            return
        }
        
        // 基本信息
        print("📱 用戶 ID: \(info.originalAppUserId)")
        print("🆔 顧客 ID: \(info.originalApplicationVersion ?? "未知")")
        print("📅 信息請求時間: \(info.requestDate)")
        
        // Pro 權益詳細信息
        if let proEntitlement = info.entitlements["pro"] {
            print("🎯 Pro 權益狀態:")
            print("   ✅ RevenueCat isActive: \(proEntitlement.isActive)")
            print("   🔄 是否會續訂: \(proEntitlement.willRenew)")
            print("   📅 過期日期: \(proEntitlement.expirationDate?.description ?? "無")")
            print("   🛒 產品 ID: \(proEntitlement.productIdentifier)")
            print("   📦 購買日期: \(proEntitlement.latestPurchaseDate?.description ?? "無")")
            // print("   🏪 商店: \(proEntitlement.store.description)")
            
            // 手動檢查是否過期（與 RevenueCat 的 isActive 對比）
            if let expirationDate = proEntitlement.expirationDate {
                let now = Date()
                let isActuallyExpired = expirationDate < now
                let revenueCatSaysActive = proEntitlement.isActive
                
                print("   ⏰ 實際是否已過期: \(isActuallyExpired)")
                print("   🔍 RevenueCat 說是否啟用: \(revenueCatSaysActive)")
                
                // 檢查是否有不一致的情況
                if isActuallyExpired && revenueCatSaysActive {
                    print("   ⚠️  警告：過期時間已過但 RevenueCat 仍顯示啟用（可能是快取問題）")
                } else if !isActuallyExpired && !revenueCatSaysActive {
                    print("   ⚠️  警告：未過期但 RevenueCat 顯示未啟用")
                } else {
                    print("   ✅ RevenueCat 狀態與實際過期時間一致")
                }
                
                if !isActuallyExpired {
                    let timeRemaining = expirationDate.timeIntervalSince(now)
                    let daysRemaining = Int(timeRemaining / 86400) // 86400 秒 = 1 天
                    let hoursRemaining = Int((timeRemaining.truncatingRemainder(dividingBy: 86400)) / 3600)
                    print("   ⏳ 剩餘時間: \(daysRemaining) 天 \(hoursRemaining) 小時")
                } else {
                    let timeExpired = now.timeIntervalSince(expirationDate)
                    let daysExpired = Int(timeExpired / 86400)
                    let hoursExpired = Int((timeExpired.truncatingRemainder(dividingBy: 86400)) / 3600)
                    print("   ⏰ 已過期: \(daysExpired) 天 \(hoursExpired) 小時")
                }
            }
            
            // 檢查訂閱類型
            if proEntitlement.periodType == .trial {
                print("   🆓 訂閱類型: 試用期")
            } else if proEntitlement.periodType == .normal {
                print("   💰 訂閱類型: 正式訂閱")
            } else {
                print("   ❓ 訂閱類型: 未知")
            }
            
            // 檢查快取時間差
            let cacheAge = Date().timeIntervalSince(info.requestDate)
            print("   🕐 快取年齡: \(Int(cacheAge)) 秒")
            if cacheAge > 300 { // 5分鐘
                print("   ⚠️  快取可能過舊（超過5分鐘）")
            }
            
        } else {
            print("❌ 未找到 Pro 權益")
        }
        
        // 所有啟用的權益
        print("🎁 所有啟用的權益:")
        if info.entitlements.active.isEmpty {
            print("   無啟用的權益")
        } else {
            for (key, entitlement) in info.entitlements.active {
                print("   - \(key): \(entitlement.productIdentifier)")
            }
        }
        
        // 所有購買的產品
        print("🛍️ 所有購買記錄:")
        if info.allPurchasedProductIdentifiers.isEmpty {
            print("   無購買記錄")
        } else {
            for productId in info.allPurchasedProductIdentifiers {
                print("   - \(productId)")
            }
        }
        
        print("=== 訂閱狀態信息結束 ===")
    }
    
    // 檢查並更新 Pro 訂閱狀態
    private func updateProStatus(with customerInfo: CustomerInfo?) {
        guard let customerInfo = customerInfo else {
            print("⚠️ 無顧客信息，設定 Pro 狀態為 false")
            self.isPro = false
            return
        }
        
        let oldValue = self.isPro
        // 檢查用戶是否有 pro 權益
        let newProStatus = customerInfo.entitlements[proEntitlementId]?.isActive == true
        self.isPro = newProStatus
        
        print("🔄 Pro 狀態更新: \(oldValue) -> \(newProStatus)")
        
        // 如果 Pro 狀態發生變化，發送通知
        if oldValue != self.isPro {
            print("📢 Pro 狀態變化，發送通知")
            NotificationCenter.default.post(name: .proStatusChanged, object: nil)
        }
    }
    
    // MARK: - 檢查訂閱狀態
    
    /// 手動觸發訂閱狀態日誌輸出（用於調試）
    func printCurrentSubscriptionStatus() {
        print("🔍 手動檢查當前訂閱狀態（使用快取）")
        logSubscriptionStatus(customerInfo)
        print("📱 當前 isPro 狀態: \(isPro)")
    }
    
    /// 強制從服務器刷新並輸出訂閱狀態（用於調試）
    func forceRefreshAndPrintStatus() {
        print("🔄 強制刷新並檢查訂閱狀態")
        updateCustomerInfo(forceRefresh: true)
    }
    
    /// 檢查特定權益是否啟用
    /// - Parameter entitlementId: 權益 ID
    /// - Returns: 是否啟用
    func isEntitlementActive(_ entitlementId: String = "pro") -> Bool {
        return customerInfo?.entitlements[entitlementId]?.isActive == true
    }
    
    /// 檢查用戶是否有任何啟用的權益
    /// - Returns: 是否有任何啟用的權益
    func hasActiveEntitlements() -> Bool {
        return !(customerInfo?.entitlements.active.isEmpty ?? true)
    }
    
    /// 獲取訂閱過期日期
    /// - Parameter entitlementId: 權益 ID
    /// - Returns: 過期日期（如果存在）
    func getExpirationDate(for entitlementId: String = "pro") -> Date? {
        return customerInfo?.entitlements[entitlementId]?.expirationDate
    }
    
    /// 獲取訂閱信息詳情
    /// - Parameter entitlementId: 權益 ID
    /// - Returns: 權益信息
    func getEntitlementInfo(for entitlementId: String = "pro") -> EntitlementInfo? {
        return customerInfo?.entitlements[entitlementId]
    }
    
    // MARK: - 獲取商品信息
    func getOfferings(completion: ((Offerings?) -> Void)? = nil) {
        Purchases.shared.getOfferings { [weak self] (offerings, error) in
            guard let self = self else { return }
            
            if let error = error {
                print("獲取商品錯誤: \(error.localizedDescription)")
                completion?(nil)
                return
            }
            
            DispatchQueue.main.async {
                self.offerings = offerings
                completion?(offerings)
            }
        }
    }
    
    // MARK: - 購買產品
    func purchasePackage(_ package: Package, completion: @escaping (Bool, CustomerInfo?, Error?) -> Void) {
        print("🛒 開始購買產品: \(package.storeProduct.localizedTitle)")
        print("💰 產品價格: \(package.storeProduct.localizedPriceString)")
        
        Purchases.shared.purchase(package: package) { [weak self] (transaction, customerInfo, error, userCancelled) in
            guard let self = self else { return }
            
            if userCancelled {
                print("❌ 用戶取消購買")
                completion(false, nil, nil)
                return
            }
            
            if let error = error {
                print("❌ 購買失敗: \(error.localizedDescription)")
                completion(false, nil, error)
                return
            }
            
            print("✅ 購買成功！")
            
            DispatchQueue.main.async {
                self.customerInfo = customerInfo
                
                // 詳細記錄購買後的訂閱狀態
                print("📦 購買完成後的訂閱狀態:")
                self.logSubscriptionStatus(customerInfo)
                
                self.updateProStatus(with: customerInfo)
                completion(true, customerInfo, nil)
            }
        }
    }
    
    // MARK: - 恢復購買
    func restorePurchases(completion: @escaping (Bool, Error?) -> Void) {
        print("🔄 開始恢復購買...")
        
        Purchases.shared.restorePurchases { [weak self] (customerInfo, error) in
            guard let self = self else { return }
            
            if let error = error {
                print("❌ 恢復購買失敗: \(error.localizedDescription)")
                completion(false, error)
                return
            }
            
            print("✅ 恢復購買完成！")
            
            DispatchQueue.main.async {
                self.customerInfo = customerInfo
                
                // 詳細記錄恢復購買後的訂閱狀態
                print("🔄 恢復購買後的訂閱狀態:")
                self.logSubscriptionStatus(customerInfo)
                
                self.updateProStatus(with: customerInfo)
                completion(true, nil)
            }
        }
    }
}

// MARK: - RevenueCat 代理實現
extension IAPService: PurchasesDelegate {
    func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        print("🔔 收到 RevenueCat 訂閱狀態更新通知")
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 記錄更新前的狀態
            let oldProStatus = self.isPro
            print("📊 更新前 Pro 狀態: \(oldProStatus)")
            
            self.customerInfo = customerInfo
            
            // 詳細記錄新的訂閱狀態
            self.logSubscriptionStatus(customerInfo)
            
            self.updateProStatus(with: customerInfo)
            
            print("📊 更新後 Pro 狀態: \(self.isPro)")
            
            // 調用註冊的更新處理程序
            self.customerInfoUpdateHandler?(customerInfo)
        }
    }
} 