// CalculatorEngine.swift
// 計算引擎服務，處理數學表達式計算

import Foundation
import MathParser

class CalculatorEngine {
    
    // MARK: - 錯誤類型
    enum CalculationError: Error {
        case invalidExpression
        case divisionByZero
        case other(String)
        
        var message: String {
            switch self {
            case .invalidExpression:
                return "calculation_error".localized
            case .divisionByZero:
                return "division_by_zero".localized
            case .other(let message):
                return message
            }
        }
    }
    
    // MARK: - 屬性
    
    /// 是否啟用隱式乘法（當沒有明確的乘法運算符時）
    private let enableImpliedMultiplication: Bool
    
    /// MathParser引擎
    private let parser: MathParser
    
    /// 數字格式服務
    private let numberFormatService = NumberFormatService.shared
    
    // MARK: - 初始化
    
    /// 初始化計算引擎
    /// - Parameter enableImpliedMultiplication: 是否啟用隱式乘法，預設為true
    init(enableImpliedMultiplication: Bool = true) {
        self.enableImpliedMultiplication = enableImpliedMultiplication
        self.parser = MathParser(enableImpliedMultiplication: enableImpliedMultiplication)
    }
    
    // MARK: - 計算方法
    
    /// 計算數學表達式
    /// - Parameter expression: 要計算的表達式字符串
    /// - Returns: 計算結果
    func calculate(_ expression: String) throws -> Double {
        // 處理表達式，移除空格
        let processedExpression = expression.replacingOccurrences(of: " ", with: "")
        
        // 調試輸出
        print("處理後的表達式: \(processedExpression)")
        
        // 檢查空括號
        if processedExpression.contains("()") {
            throw CalculationError.invalidExpression
        }
    
        // 預處理表達式，替換乘除符號
        let standardizedExpression = processedExpression
            .replacingOccurrences(of: "×", with: "*")
            .replacingOccurrences(of: "÷", with: "/")
        
        // 特殊處理減法表達式
        // 確保減號前後有足夠空間，避免被誤解為負號
        let fixedExpression = standardizedExpression
            .replacingOccurrences(of: "-", with: " - ")
            .replacingOccurrences(of: "  -  ", with: " - ") // 修正可能的多餘空格
            .replacingOccurrences(of: "+ -", with: "- ")    // 修正 "+ -" 組合
            .replacingOccurrences(of: "* -", with: "* -")   // 保持 "* -" 結構
            .replacingOccurrences(of: "/ -", with: "/ -")   // 保持 "/ -" 結構
            .replacingOccurrences(of: "( -", with: "(-")    // 修正左括號後的負號
        
        print("修正後用於計算的表達式: \(fixedExpression)")
        
        // 使用MathParser庫解析表達式
        let result = parser.parseResult(fixedExpression)
        
        switch result {
        case .success(let evaluator):
            let value = evaluator.value
            
            // 檢查是否為NaN或無限
            if value.isNaN {
                throw CalculationError.invalidExpression
            }
            if value.isInfinite {
                throw CalculationError.divisionByZero
            }
            
            print("計算成功，結果: \(value)")
            return value
            
        case .failure(let error):
            // 處理解析錯誤
            print("解析錯誤: \(error)")
            
            // 檢查是否為除零錯誤
            let errorString = String(describing: error)
            if errorString.contains("divide by zero") || errorString.contains("division by zero") {
            throw CalculationError.divisionByZero
            } else {
                // 嘗試使用備用計算方法
                if fixedExpression.contains("-") {
                    print("檢測到減法運算，嘗試使用特殊處理")
                    if let result = try? specialCaseCalculation(fixedExpression) {
                        print("使用特殊情況處理減法成功: \(result)")
                        return result
                    }
                }
                
                if fixedExpression.contains("*") && fixedExpression.contains("/") {
                    if let result = try? specialCaseCalculation(fixedExpression) {
                        print("使用特殊情況處理乘除法成功: \(result)")
                        return result
                    }
                }
                
                throw CalculationError.invalidExpression
            }
        }
    }
    
    // MARK: - 私有輔助方法
    
    /// 特殊情況計算 - 處理連續乘除法問題（備用方法）
    private func specialCaseCalculation(_ expression: String) throws -> Double {
        // 簡單的計算器，特別針對連續乘除法和連續減法問題
        
        // 調試信息
        print("特殊情況處理表達式: \(expression)")
        
        // 預處理表達式，處理連續的減法符號和負號
        var processedExpression = expression
        
        // 將 "- -" 替換為 "+"（兩個負號相當於正號）
        while processedExpression.contains("- -") {
            processedExpression = processedExpression.replacingOccurrences(of: "- -", with: "+")
        }
        
        // 將 "+ -" 替換為 "-"
        while processedExpression.contains("+ -") {
            processedExpression = processedExpression.replacingOccurrences(of: "+ -", with: "-")
        }
        
        // 將 "--" 替換為 "+"（可能出現在負數後接減法的情況）
        while processedExpression.contains("--") {
            processedExpression = processedExpression.replacingOccurrences(of: "--", with: "+")
        }
        
        print("預處理後的表達式: \(processedExpression)")
        
        // 分解表達式
        let components = processedExpression.components(separatedBy: CharacterSet(charactersIn: "+-*/()"))
            .filter { !$0.isEmpty }
        
        print("表達式組件: \(components)")
        
        // 將字符串轉換為Decimal數值
        var tokens: [Decimal] = []
        for component in components {
            if let decimal = Decimal(string: component.trimmingCharacters(in: .whitespaces)) {
                tokens.append(decimal)
            } else {
                print("無法解析組件: \(component)")
            throw CalculationError.invalidExpression
        }
        }
        
        print("數值組件: \(tokens)")
        
        // 構建運算符列表
        var operators: [Character] = []
        var isNegativeFirst = false
        
        // 檢查表達式是否以負號開始
        if processedExpression.hasPrefix("-") {
            isNegativeFirst = true
        }
        
        for char in processedExpression {
            if "+-*/".contains(char) {
                operators.append(char)
            }
        }
        
        print("運算符列表: \(operators)")
        
        // 處理表達式開頭為負數的情況
        if isNegativeFirst && !tokens.isEmpty {
            // 如果表達式以負號開始，第一個數應該是負數
            tokens[0] = -tokens[0]
            // 並且從運算符列表中移除第一個減號
            if !operators.isEmpty && operators[0] == "-" {
                operators.removeFirst()
            }
        }
        
        print("處理後的數值組件: \(tokens)")
        print("處理後的運算符列表: \(operators)")
        
        // 如果我們有有效的表達式結構
        if !tokens.isEmpty && (tokens.count == operators.count + 1 || 
                               (isNegativeFirst && tokens.count == operators.count)) {
            var result = tokens[0]
            
            // 計算表達式結果
            for i in 0..<operators.count {
                let op = operators[i]
                let operandIndex = i + 1
                
                // 確保我們有足夠的操作數
                if operandIndex < tokens.count {
                    let operand = tokens[operandIndex]
                    
                    switch op {
                    case "*":
                        result *= operand
                    case "/":
                        // 避免除以零
                        if operand == 0 {
                            throw CalculationError.divisionByZero
                        }
                        result /= operand
                    case "+":
                        result += operand
                    case "-":
                        result -= operand
                    default:
                        break
                    }
                    
                    print("操作: \(result) \(op) \(operand) = \(result)")
                }
            }
            
            // 轉換回 Double
            return NSDecimalNumber(decimal: result).doubleValue
        }
        
        // 如果不是我們能處理的特殊情況，拋出錯誤
        print("無法處理的表達式結構")
        throw CalculationError.invalidExpression
    }
    
    /// 格式化結果為字符串（使用系統數字格式）
    func formatResult(_ result: Double) -> String {
        // 處理特殊情況
        if result.isInfinite || result.isNaN {
            return "Error"
        }
        
        // 🔧 修復：移除格式化，保持純數字
        return cleanNumberString(result)
    }
    
    /// 🔧 清理數字字符串，移除所有格式化，保持純數字（底層數據）
    private func cleanNumberString(_ number: Double) -> String {
        // 處理特殊情況
        if number.isInfinite || number.isNaN {
            return "Error"
        }
        
        // 檢查是否為整數
        if number == floor(number) {
            // 整數：直接轉換，不要小數點
            return String(format: "%.0f", number)
        } else {
            // 小數：移除尾部零，但保留必要的小數位
            let formatter = NumberFormatter()
            formatter.numberStyle = .decimal
            formatter.usesGroupingSeparator = false // 🔧 關鍵：不使用千位分隔符
            formatter.minimumFractionDigits = 0
            formatter.maximumFractionDigits = 10
            formatter.locale = Locale(identifier: "en_US") // 🔧 底層數據使用標準格式，確保小數點是 "."
            
            if let formatted = formatter.string(from: NSNumber(value: number)) {
                return formatted
            }
            
            // 備用方案：確保使用標準點
            return String(number)
        }
    }
    
    /// 格式化整數結果（已棄用，使用 formatResult 代替）
    private func formatIntegerResult(_ decimalNumber: Decimal) -> String {
        let doubleValue = NSDecimalNumber(decimal: decimalNumber).doubleValue
        
        // 大數使用科學計數法
        if abs(doubleValue) > 999999999 {
            return formatScientificNotation(doubleValue)
        }
        
        // 使用系統數字格式服務
        return numberFormatService.formatNumberWithFormatter(doubleValue)
    }
    
    /// 格式化小數結果（已棄用，使用 formatResult 代替）
    private func formatDecimalResult(_ decimalNumber: Decimal) -> String {
        let doubleValue = NSDecimalNumber(decimal: decimalNumber).doubleValue
        
        // 極小數使用科學計數法
        if abs(doubleValue) > 0 && abs(doubleValue) < 0.0001 {
            return formatScientificNotation(doubleValue)
        }
        
        // 使用系統數字格式服務
        return numberFormatService.formatNumberWithFormatter(doubleValue)
    }
    
    /// 移除尾部的零
    private func removeTrailingZeros(_ string: String) -> String {
        var result = string
        
        if result.contains(".") || result.contains(numberFormatService.decimalSeparator) {
            while result.hasSuffix("0") {
                result.removeLast()
            }
            
            if result.hasSuffix(".") || result.hasSuffix(numberFormatService.decimalSeparator) {
                result.removeLast()
            }
        }
        
        return result
    }
    
    /// 格式化科學計數法
    private func formatScientificNotation(_ value: Double) -> String {
        let formatter = NumberFormatter()
        formatter.locale = Locale.current
        formatter.numberStyle = .scientific
        formatter.exponentSymbol = "e"
        formatter.minimumFractionDigits = 2
        formatter.maximumFractionDigits = 2
        
        if let formatted = formatter.string(from: NSNumber(value: value)) {
            return formatted
        }
        
        return String(format: "%.2e", value)
    }
    
    /// 確定適合的小數位數
    private func determinePrecision(for value: Double) -> Int {
        let absValue = abs(value)
        
        // 針對非常小的數值增加精度
        if absValue < 0.0001 {
            return 10
        } else if absValue < 0.01 {
            return 8
        } else if absValue < 1 {
            return 6
        } else if absValue < 1000 {
            return 4
        } else {
            return 2
        }
    }
}

// MARK: - Decimal 擴展
extension Decimal {
    var isInteger: Bool {
        // 多重檢查，確保整數識別的準確性
        
        // 1. 檢查是否與取整值的差異非常小
        let doubleValue = NSDecimalNumber(decimal: self).doubleValue
        let roundedValue = doubleValue.rounded()
        let epsilon = 1e-10
        if abs(roundedValue - doubleValue) < epsilon {
            return true
        }
        
        // 2. 嘗試使用字符串表示法進行判斷
        let stringValue = NSDecimalNumber(decimal: self).stringValue
        if !stringValue.contains(".") {
            return true
        }
        
        // 3. 檢查小數部分是否為零（考慮科學計數法）
        if let decimalPoint = stringValue.firstIndex(of: ".") {
            let fractionalPart = stringValue[stringValue.index(after: decimalPoint)...]
            if fractionalPart.allSatisfy({ $0 == "0" }) {
                return true
            }
        }
        
        return false
    }
}

// MARK: - 說明：將 DDMathParser 整合到專案中
/*
 為了完整解決數學表達式計算的精度問題，建議添加 DDMathParser 框架到專案中：
 
 1. 在 Xcode 中，選擇 "File" > "Add Packages..."
 2. 在搜索欄中輸入 https://github.com/davedelong/DDMathParser
 3. 選擇並添加框架到專案
 
 然後修改 calculateWithHighPrecision 方法：
 
 ```swift
 import DDMathParser
 
 private func calculateWithHighPrecision(_ expression: String) throws -> Double {
     do {
         // 使用 DDMathParser 解析和計算表達式
         let evaluator = MathEvaluator.default
         let result = try evaluator.evaluate(expression)
         return result
     } catch {
         print("DDMathParser 錯誤: \(error)")
         throw CalculationError.invalidExpression
     }
 }
 ```
 
 DDMathParser 提供了:
 - 更好的表達式解析
 - 更高的計算精度
 - 更好的錯誤處理
 - 更多數學函數支持
 - 自定義運算符和常量
 */ 
