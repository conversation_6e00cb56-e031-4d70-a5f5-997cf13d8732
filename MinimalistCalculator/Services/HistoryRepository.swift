// HistoryRepository.swift
// 計算器歷史記錄存儲服務

import Foundation
import Combine

// 定義通知名稱
extension Notification.Name {
    static let historyDataChanged = Notification.Name("historyDataChanged")
}

class HistoryRepository: ObservableObject {
    // MARK: - 單例設計模式
    static let shared = HistoryRepository()
    
    // MARK: - 常量
    private let historyKey = "calculatorHistory"
    private let freeVersionMaxEntries = 2
    
    // MARK: - 依賴
    private let iapService = IAPService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    private init() {
        // 監聽 Pro 狀態變化通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleProStatusChanged),
            name: .proStatusChanged,
            object: nil
        )
        
        // 監聽 IAPService 初始化完成通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleIAPServiceInitialized),
            name: .iapServiceInitialized,
            object: nil
        )
        
        // 監聽 IAPService 的 isPro 屬性變化
        iapService.$isPro
            .sink { [weak self] _ in
                self?.objectWillChange.send()
            }
            .store(in: &cancellables)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        cancellables.forEach { $0.cancel() }
    }
    
    // MARK: - 通知處理
    
    /// 處理 Pro 狀態變化通知
    @objc private func handleProStatusChanged() {
        // 當 Pro 狀態變化時，通知 UI 刷新歷史記錄列表
        objectWillChange.send()
        NotificationCenter.default.post(name: .historyDataChanged, object: nil)
    }
    
    /// 處理 IAPService 初始化完成通知
    @objc private func handleIAPServiceInitialized() {
        // 當 IAPService 初始化完成時，通知 UI 刷新歷史記錄列表
        objectWillChange.send()
        NotificationCenter.default.post(name: .historyDataChanged, object: nil)
    }
    
    // MARK: - 讀取方法
    
    /// 獲取所有歷史記錄
    /// - Returns: 歷史記錄數組，按時間降序排序
    func getAllHistory() -> [HistoryEntry] {
        // 確保每次都重新檢查 Pro 狀態
        let isPro = iapService.isEntitlementActive("pro")
        return getAllHistory(includeAll: isPro)
    }
    
    /// 獲取當前的歷史記錄限制
    /// - Returns: 如果是 Pro 版本返回 nil（表示無限制），否則返回免費版本的限制數量
    func getCurrentHistoryLimit() -> Int? {
        // 確保每次都重新檢查 Pro 狀態
        let isPro = iapService.isEntitlementActive("pro")
        return isPro ? nil : freeVersionMaxEntries
    }
    
    /// 獲取歷史記錄總數（包括可能被限制顯示的記錄）
    /// - Returns: 歷史記錄總數
    func getTotalHistoryCount() -> Int {
        return getAllHistory(includeAll: true).count
    }
    
    /// 獲取隱藏的歷史記錄數量（由於免費版本限制而隱藏的記錄數量）
    /// - Returns: 隱藏的記錄數量
    func getHiddenHistoryCount() -> Int {
        let total = getTotalHistoryCount()
        // 確保每次都重新檢查 Pro 狀態
        let isPro = iapService.isEntitlementActive("pro")
        if isPro {
            return 0
        } else {
            return total > freeVersionMaxEntries ? total - freeVersionMaxEntries : 0
        }
    }
    
    // MARK: - 寫入方法
    
    /// 添加新的歷史記錄條目
    /// - Parameter entry: 要添加的歷史記錄
    func addEntry(_ entry: HistoryEntry) {
        var currentHistory = getAllHistory(includeAll: true)
        
        // 添加新條目
        currentHistory.insert(entry, at: 0)
        
        // 保存到 UserDefaults (不限制數量，在讀取時進行限制)
        saveHistory(currentHistory)
        
        // 通知數據變化
        objectWillChange.send()
        NotificationCenter.default.post(name: .historyDataChanged, object: nil)
    }
    
    /// 清除所有歷史記錄
    func clearAllHistory() {
        UserDefaults.standard.removeObject(forKey: historyKey)
        
        // 通知數據變化
        objectWillChange.send()
        NotificationCenter.default.post(name: .historyDataChanged, object: nil)
    }
    
    // MARK: - 輔助方法
    
    /// 獲取所有歷史記錄，可選是否包含所有記錄（不受限制）
    /// - Parameter includeAll: 是否包含所有記錄，不受 Pro 版本限制
    /// - Returns: 歷史記錄數組
    private func getAllHistory(includeAll: Bool = false) -> [HistoryEntry] {
        guard let data = UserDefaults.standard.data(forKey: historyKey) else {
            return []
        }
        
        do {
            let entries = try JSONDecoder().decode([HistoryEntry].self, from: data)
            // 按日期降序排序（最新的在前面）
            let sortedEntries = entries.sorted { $0.date > $1.date }
            
            // 如果需要包含所有記錄或是 Pro 版本，則返回所有記錄
            if includeAll || iapService.isEntitlementActive("pro") {
                return sortedEntries
            }
            
            // 否則限制歷史記錄數量
            return Array(sortedEntries.prefix(freeVersionMaxEntries))
        } catch {
            Logger.error("Error decoding history: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 保存歷史記錄到 UserDefaults
    /// - Parameter history: 要保存的歷史記錄數組
    private func saveHistory(_ history: [HistoryEntry]) {
        do {
            let data = try JSONEncoder().encode(history)
            UserDefaults.standard.set(data, forKey: historyKey)
        } catch {
            print("Error encoding history: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 公共方法
    
    /// 強制刷新 Pro 狀態，並更新歷史記錄
    func refreshProStatus() {
        // 請求 IAPService 更新顧客信息
        iapService.updateCustomerInfo()
        
        // 通知 UI 刷新
        objectWillChange.send()
    }
} 