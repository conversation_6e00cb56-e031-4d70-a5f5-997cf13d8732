// NumberFormatService.swift
// 數字格式服務，偵測和處理系統的數字格式設定

import Foundation

// MARK: - 通知名稱擴展
extension Notification.Name {
    static let numberFormatChanged = Notification.Name("NumberFormatChanged")
}

class NumberFormatService: ObservableObject {
    
    // MARK: - 單例
    static let shared = NumberFormatService()
    
    // MARK: - 數字格式類型
    enum NumberFormat {
        case anglophone    // 1,234,567.89 (英語系國家)
        case european      // 1.234.567,89 (歐洲大部分國家)
        case spaceDot      // 1 234 567.89 (瑞士、加拿大法語區等)
        case spaceComma    // 1 234 567,89 (法國、俄羅斯等)
        
        var thousandsSeparator: String {
            switch self {
            case .anglophone: return ","
            case .european: return "."
            case .spaceComma, .spaceDot: return " "
            }
        }
        
        var decimalSeparator: String {
            switch self {
            case .anglophone, .spaceDot: return "."
            case .european, .spaceComma: return ","
            }
        }
        
        var description: String {
            switch self {
            case .anglophone: return "1,234,567.89"
            case .european: return "1.234.567,89"
            case .spaceComma: return "1 234 567,89"
            case .spaceDot: return "1 234 567.89"
            }
        }
    }
    
    // MARK: - 屬性
    @Published private(set) var currentFormat: NumberFormat
    
    // MARK: - 初始化
    private init() {
        self.currentFormat = Self.detectNumberFormat(from: Locale.autoupdatingCurrent)
        
        // 監聽系統 locale 變化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(localeDidChange),
            name: NSLocale.currentLocaleDidChangeNotification,
            object: nil
        )
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - 公開方法
    
    /// 格式化數字字符串，使用 Apple 原生 NumberFormatter
    func formatNumber(_ numberString: String) -> String {
        // 檢查是否是操作符或括號等非數字
        if numberString == "+" || numberString == "-" || numberString == "*" || numberString == "/" || 
           numberString == "(" || numberString == ")" || numberString.isEmpty || numberString.lowercased().contains("error") {
            return numberString
        }
        
        // 檢查是否是科學計數法
        if numberString.lowercased().contains("e") {
            return numberString  // 科學計數法保持原樣
        }
        
        // 使用 Apple 原生方法解析和格式化
        return formatNumberWithFormatter(numberString)
    }
    
    /// 將格式化的數字字符串轉換回標準格式（用於計算）
    func parseFormattedNumber(_ formattedString: String) -> String {
        // 使用 Apple 原生方法解析
        if let number = parseNumberFromAnyFormat(formattedString) {
            return String(number.doubleValue)
        }
        return formattedString
    }
    
    /// 使用 Apple 原生 NumberFormatter 格式化數字
    func formatNumberWithFormatter(_ input: Any) -> String {
        let formatter = NumberFormatter()
        formatter.locale = Locale.autoupdatingCurrent
        formatter.numberStyle = .decimal
        formatter.usesGroupingSeparator = true
        formatter.maximumFractionDigits = 10
        formatter.minimumFractionDigits = 0
        
        // 處理不同類型的輸入
        var number: NSNumber?
        
        if let doubleValue = input as? Double {
            number = NSNumber(value: doubleValue)
        } else if let stringValue = input as? String {
            number = parseNumberFromAnyFormat(stringValue)
        } else if let nsNumber = input as? NSNumber {
            number = nsNumber
        }
        
        guard let validNumber = number else {
            return input as? String ?? "0"
        }
        
        return formatter.string(from: validNumber) ?? String(validNumber.doubleValue)
    }
    
    /// 使用多種 locale 嘗試解析數字字符串
    private func parseNumberFromAnyFormat(_ numberString: String) -> NSNumber? {
        // 首先嘗試用當前 locale 解析
        let currentFormatter = NumberFormatter()
        currentFormatter.locale = Locale.autoupdatingCurrent
        currentFormatter.numberStyle = .decimal
        
        if let number = currentFormatter.number(from: numberString) {
            return number
        }
        
        // 如果失敗，嘗試用常見的 locale 解析
        let commonLocales = ["en_US", "de_DE", "fr_FR", "zh_TW"]
        
        for localeId in commonLocales {
            let locale = Locale(identifier: localeId)
            let formatter = NumberFormatter()
            formatter.locale = locale
            formatter.numberStyle = .decimal
            
            if let number = formatter.number(from: numberString) {
                return number
            }
        }
        
        // 最後嘗試直接用 Double 解析
        if let doubleValue = Double(numberString) {
            return NSNumber(value: doubleValue)
        }
        
        return nil
    }
    
    /// 獲取當前數字格式的示例
    func getFormatExample() -> String {
        return currentFormat.description
    }
    
    /// 獲取千位分隔符
    var thousandsSeparator: String {
        return currentFormat.thousandsSeparator
    }
    
    /// 獲取小數分隔符
    var decimalSeparator: String {
        return currentFormat.decimalSeparator
    }
    
    /// 手動刷新數字格式偵測（用於 app 從背景回到前景時）
    func refreshNumberFormat() {
        let newFormat = Self.detectNumberFormat(from: Locale.autoupdatingCurrent)
        if newFormat != self.currentFormat {
            DispatchQueue.main.async { [weak self] in
                self?.currentFormat = newFormat
                Logger.info("數字格式已手動刷新為: \(newFormat.description)")

                // 發送數字格式變化通知
                NotificationCenter.default.post(name: .numberFormatChanged, object: nil)
            }
        }
    }
    
    /// 🎭 顯示層格式化：僅用於 UI 顯示，不影響底層計算邏輯
    func formatForDisplay(_ numberString: String) -> String {
        // 檢查是否是操作符或特殊值
        if numberString == "+" || numberString == "-" || numberString == "*" || numberString == "/" || 
           numberString == "(" || numberString == ")" || numberString.isEmpty || 
           numberString.lowercased().contains("error") || numberString == "0" {
            return numberString
        }
        
        // 檢查是否是科學計數法
        if numberString.lowercased().contains("e") {
            return numberString
        }
        
        // 嘗試解析為數字
        guard let number = Double(numberString) else {
            return numberString
        }
        
        // 使用系統格式化器添加千位分隔符
        let formatter = NumberFormatter()
        formatter.locale = Locale.autoupdatingCurrent
        formatter.numberStyle = .decimal
        formatter.usesGroupingSeparator = true // 🔧 啟用千位分隔符
        formatter.maximumFractionDigits = 10
        formatter.minimumFractionDigits = 0
        
        if let formatted = formatter.string(from: NSNumber(value: number)) {
            return formatted
        }
        
        // 備用方案
        return numberString
    }
    
    /// 🔧 解析顯示格式化的數字回原始格式（用於計算）
    func parseDisplayFormatted(_ formattedString: String) -> String {
        // 移除千位分隔符，保留小數分隔符
        var cleaned = formattedString
        
        // 移除千位分隔符
        cleaned = cleaned.replacingOccurrences(of: thousandsSeparator, with: "")
        
        // 將小數分隔符轉換為標準點
        if decimalSeparator != "." {
            cleaned = cleaned.replacingOccurrences(of: decimalSeparator, with: ".")
        }
        
        return cleaned
    }
    
    // MARK: - 私有方法
    
    /// 偵測系統的數字格式
    private static func detectNumberFormat(from locale: Locale) -> NumberFormat {
        let formatter = NumberFormatter()
        formatter.locale = locale
        formatter.numberStyle = .decimal
        formatter.usesGroupingSeparator = true
        
        // 使用一個測試數字來偵測格式
        let testNumber = 1234567.89
        guard formatter.string(from: NSNumber(value: testNumber)) != nil else {
            return .anglophone // 預設格式
        }
        
        // 分析格式化結果
        let thousandsSep = formatter.groupingSeparator ?? ","
        let decimalSep = formatter.decimalSeparator ?? "."
        
        // 根據分隔符組合判斷格式
        switch (thousandsSep, decimalSep) {
        case (",", "."):
            return .anglophone
        case (".", ","):
            return .european
        case (" ", ","):
            return .spaceComma
        case (" ", "."):
            return .spaceDot
        default:
            // 對於其他組合，根據 locale 的地區代碼進行判斷
            return detectByRegion(locale: locale)
        }
    }
    
    /// 根據地區代碼偵測數字格式
    private static func detectByRegion(locale: Locale) -> NumberFormat {
        guard let regionCode = locale.regionCode else {
            return .anglophone
        }
        
        // 歐洲國家使用歐洲格式
        let europeanCountries = ["DE", "IT", "ES", "PT", "NL", "BE", "AT", "GR", "FI", "EE", "LV", "LT", "SK", "SI", "MT", "CY"]
        if europeanCountries.contains(regionCode) {
            return .european
        }
        
        // 使用空格作為千位分隔符的國家
        let spaceCountries = ["FR", "RU", "PL", "CZ", "HU", "RO", "BG", "HR", "SE", "NO", "DK"]
        if spaceCountries.contains(regionCode) {
            return locale.decimalSeparator == "," ? .spaceComma : .spaceDot
        }
        
        // 其他國家預設使用英語格式
        return .anglophone
    }
    
    /// 處理系統 locale 變化
    @objc private func localeDidChange() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let newFormat = Self.detectNumberFormat(from: Locale.autoupdatingCurrent)
            if newFormat != self.currentFormat {
                self.currentFormat = newFormat
                Logger.info("數字格式因系統 locale 變化而更新為: \(newFormat.description)")

                // 發送數字格式變化通知
                NotificationCenter.default.post(name: .numberFormatChanged, object: nil)
            }
        }
    }
}

// MARK: - NumberFormat Equatable
extension NumberFormatService.NumberFormat: Equatable {
    static func == (lhs: NumberFormatService.NumberFormat, rhs: NumberFormatService.NumberFormat) -> Bool {
        switch (lhs, rhs) {
        case (.anglophone, .anglophone),
             (.european, .european),
             (.spaceComma, .spaceComma),
             (.spaceDot, .spaceDot):
            return true
        default:
            return false
        }
    }
} 