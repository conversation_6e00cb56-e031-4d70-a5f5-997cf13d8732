// HapticService.swift
// 觸覺反饋管理服務

import Foundation
import UIKit

// MARK: - 通知名稱擴展
extension Notification.Name {
    static let hapticSettingChanged = Notification.Name("hapticSettingChanged")
}

class HapticService: ObservableObject {
    // MARK: - 單例
    static let shared = HapticService()
    
    // MARK: - 屬性
    @Published var isHapticEnabled: Bool {
        didSet {
            UserDefaults.standard.set(isHapticEnabled, forKey: hapticEnabledKey)
            NotificationCenter.default.post(name: .hapticSettingChanged, object: nil)
        }
    }
    
    // MARK: - 常量
    private let hapticEnabledKey = "hapticEnabled"
    
    // MARK: - 觸覺反饋生成器
    private let heavyImpactFeedback = UIImpactFeedbackGenerator(style: .heavy)
    private let mediumImpactFeedback = UIImpactFeedbackGenerator(style: .medium)
    private let lightImpactFeedback = UIImpactFeedbackGenerator(style: .light)
    private let softImpactFeedback = UIImpactFeedbackGenerator(style: .soft)
    
    // MARK: - 初始化
    private init() {
        // 從 UserDefaults 讀取設定，預設為開啟
        self.isHapticEnabled = UserDefaults.standard.object(forKey: hapticEnabledKey) as? Bool ?? true
        
        // 預先準備觸覺反饋生成器
        prepareHapticGenerators()
    }
    
    // MARK: - 公開方法
    
    /// 切換觸覺反饋設定
    func toggleHaptic() {
        isHapticEnabled.toggle()
    }
    
    /// 設定觸覺反饋狀態
    /// - Parameter enabled: 是否啟用觸覺反饋
    func setHapticEnabled(_ enabled: Bool) {
        isHapticEnabled = enabled
    }
    
    /// 觸發數字按鈕觸覺反饋
    func triggerDigitHaptic() {
        guard isHapticEnabled else { return }
        mediumImpactFeedback.impactOccurred()
    }
    
    /// 觸發等號和清除按鈕觸覺反饋
    func triggerActionHaptic() {
        guard isHapticEnabled else { return }
        heavyImpactFeedback.impactOccurred()
    }
    
    /// 觸發退格按鈕觸覺反饋
    func triggerBackspaceHaptic() {
        guard isHapticEnabled else { return }
        lightImpactFeedback.impactOccurred()
    }
    
    /// 觸發其他符號按鈕觸覺反饋
    func triggerSymbolHaptic() {
        guard isHapticEnabled else { return }
        softImpactFeedback.impactOccurred()
    }
    
    // MARK: - 私有方法
    
    /// 預先準備觸覺反饋生成器
    private func prepareHapticGenerators() {
        heavyImpactFeedback.prepare()
        mediumImpactFeedback.prepare()
        lightImpactFeedback.prepare()
        softImpactFeedback.prepare()
    }
} 