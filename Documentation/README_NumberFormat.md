# 數字格式偵測功能

## 概述

這個功能讓計算器能夠自動偵測使用者的系統數字格式設定，並相應地格式化數字顯示。支援以下四種主要的數字格式：

1. **英語格式** (1,234,567.89) - 逗號作為千位分隔符，點作為小數分隔符
2. **歐洲格式** (1.234.567,89) - 點作為千位分隔符，逗號作為小數分隔符  
3. **空格逗號格式** (1 234 567,89) - 空格作為千位分隔符，逗號作為小數分隔符
4. **空格點格式** (1 234 567.89) - 空格作為千位分隔符，點作為小數分隔符

## 實現架構

### 1. NumberFormatService.swift
- **單例服務**：管理數字格式偵測和轉換
- **自動偵測**：根據系統 Locale 設定自動偵測數字格式
- **格式化方法**：提供數字格式化和解析功能
- **動態更新**：監聽系統 Locale 變化並自動更新

### 2. CalculatorEngine.swift 更新
- 整合 NumberFormatService
- 使用系統格式進行結果格式化
- 移除硬編碼的千位分隔符邏輯

### 3. CalculatorViewModel.swift 更新
- 使用 NumberFormatService 進行數字格式化
- 更新顯示值轉換邏輯
- 處理不同格式的分隔符

### 4. UI 組件
- **NumberFormatTestView.swift**：測試和展示數字格式功能
- **SettingsPageView.swift**：在設定中顯示當前數字格式

## 主要功能

### 自動偵測
```swift
// 系統會自動偵測以下設定：
let formatter = NumberFormatter()
formatter.locale = Locale.current
let thousandsSeparator = formatter.groupingSeparator
let decimalSeparator = formatter.decimalSeparator
```

### 格式化數字
```swift
let service = NumberFormatService.shared
let formatted = service.formatNumber("1234567.89")
// 根據系統設定返回：1,234,567.89 或 1.234.567,89 等
```

### 解析格式化數字
```swift
let parsed = service.parseFormattedNumber("1,234,567.89")
// 返回標準格式：1234567.89
```

## 支援的地區

### 英語格式國家
- 美國、英國、澳洲、加拿大等

### 歐洲格式國家  
- 德國、義大利、西班牙、葡萄牙、荷蘭、比利時等

### 空格分隔符國家
- 法國、俄羅斯、波蘭、捷克、匈牙利等

## 使用方式

### 1. 在計算器中
- 數字會自動根據系統設定格式化
- 輸入和顯示都會遵循系統格式
- 計算結果會使用正確的分隔符

### 2. 在設定中查看
- 進入設定頁面
- 點擊「數字格式」選項
- 查看當前偵測到的格式和測試範例

### 3. 測試功能
- 使用 NumberFormatTestView 測試不同數字
- 查看系統資訊和分隔符設定
- 即時預覽格式化結果

## 技術細節

### 偵測邏輯
1. 使用 NumberFormatter 和 Locale.current
2. 測試格式化結果分析分隔符
3. 根據地區代碼進行備用判斷
4. 監聽系統 Locale 變化

### 錯誤處理
- 無法偵測時預設使用英語格式
- 格式化失敗時使用備用方法
- 保持向後相容性

### 效能考量
- 單例模式避免重複初始化
- 快取偵測結果
- 僅在 Locale 變化時重新偵測

## 測試建議

1. **模擬器測試**：
   - 在 iOS 模擬器中變更地區設定
   - 測試不同語言和地區組合

2. **實機測試**：
   - 在實際裝置上變更系統設定
   - 驗證格式變化是否正確

3. **邊界情況**：
   - 測試極大和極小數字
   - 測試科學計數法
   - 測試負數格式化

## 未來擴展

- 支援更多地區格式
- 允許使用者手動選擇格式
- 添加貨幣格式支援
- 支援自定義分隔符 